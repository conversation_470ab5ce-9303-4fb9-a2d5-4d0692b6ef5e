"""
Market data schemas for the quantitative trading platform.
增强版市场数据响应模型
"""

from datetime import datetime
from decimal import Decimal
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, field_validator


class TickData(BaseModel):
    """Tick data schema."""

    symbol: str = Field(..., description="Symbol identifier")
    timestamp: datetime = Field(..., description="Timestamp of the tick")
    price: Decimal = Field(..., description="Last trade price")
    volume: int = Field(..., description="Trade volume")
    bid_price: Optional[Decimal] = Field(None, description="Best bid price")
    ask_price: Optional[Decimal] = Field(None, description="Best ask price")
    bid_volume: Optional[int] = Field(None, description="Best bid volume")
    ask_volume: Optional[int] = Field(None, description="Best ask volume")

    class Config:
        json_encoders = {Decimal: str, datetime: lambda v: v.isoformat()}


class KlineData(BaseModel):
    """K-line (candlestick) data schema."""

    symbol: str = Field(..., description="Symbol identifier")
    timestamp: datetime = Field(..., description="K-line timestamp")
    open_price: Decimal = Field(..., description="Opening price")
    high_price: Decimal = Field(..., description="Highest price")
    low_price: Decimal = Field(..., description="Lowest price")
    close_price: Decimal = Field(..., description="Closing price")
    volume: int = Field(..., description="Trading volume")
    amount: Optional[Decimal] = Field(None, description="Trading amount")
    interval: str = Field(..., description="Time interval (1m, 5m, 1h, 1d, etc.)")

    class Config:
        json_encoders = {Decimal: str, datetime: lambda v: v.isoformat()}


class MarketDepth(BaseModel):
    """Market depth (order book) data schema."""

    symbol: str = Field(..., description="Symbol identifier")
    timestamp: datetime = Field(..., description="Market depth timestamp")
    bids: List[List[Decimal]] = Field(..., description="Bid prices and volumes")
    asks: List[List[Decimal]] = Field(..., description="Ask prices and volumes")

    @field_validator("bids", "asks")
    @classmethod
    def validate_depth_data(cls, v):
        """Validate bid/ask depth data format."""
        if not isinstance(v, list):
            raise ValueError("Depth data must be a list")
        for item in v:
            if not isinstance(item, list) or len(item) != 2:
                raise ValueError("Each depth item must be [price, volume]")
        return v

    class Config:
        json_encoders = {Decimal: str, datetime: lambda v: v.isoformat()}


class MarketDataRequest(BaseModel):
    """Market data subscription request schema."""

    symbols: List[str] = Field(..., description="List of symbols to subscribe")
    data_types: List[str] = Field(..., description="Types of data (tick, kline, depth)")
    interval: Optional[str] = Field(None, description="K-line interval for kline data")

    @field_validator("data_types")
    @classmethod
    def validate_data_types(cls, v):
        """Validate data types."""
        valid_types = {"tick", "kline", "depth"}
        for data_type in v:
            if data_type not in valid_types:
                raise ValueError(f"Invalid data type: {data_type}")
        return v


class MarketDataResponse(BaseModel):
    """Market data response schema."""

    symbol: str = Field(..., description="Symbol identifier")
    data_type: str = Field(..., description="Type of data")
    data: Dict[str, Any] = Field(..., description="Market data payload")
    timestamp: datetime = Field(..., description="Response timestamp")

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class MarketStatus(BaseModel):
    """Market status schema."""

    market: str = Field(..., description="Market identifier")
    status: str = Field(..., description="Market status (open, closed, pre_open, etc.)")
    trading_day: str = Field(..., description="Trading day")
    open_time: Optional[datetime] = Field(None, description="Market open time")
    close_time: Optional[datetime] = Field(None, description="Market close time")

    class Config:
        json_encoders = {datetime: lambda v: v.isoformat()}


class QuoteData(BaseModel):
    """Quote data schema."""

    symbol: str = Field(..., description="Symbol identifier")
    timestamp: datetime = Field(..., description="Quote timestamp")
    bid_price: Decimal = Field(..., description="Bid price")
    ask_price: Decimal = Field(..., description="Ask price")
    bid_volume: int = Field(..., description="Bid volume")
    ask_volume: int = Field(..., description="Ask volume")
    last_price: Optional[Decimal] = Field(None, description="Last trade price")

    class Config:
        json_encoders = {Decimal: str, datetime: lambda v: v.isoformat()}


class TradeData(BaseModel):
    """Trade data schema."""

    symbol: str = Field(..., description="Symbol identifier")
    timestamp: datetime = Field(..., description="Trade timestamp")
    price: Decimal = Field(..., description="Trade price")
    volume: int = Field(..., description="Trade volume")
    direction: Optional[str] = Field(None, description="Trade direction (buy/sell)")

    class Config:
        json_encoders = {Decimal: str, datetime: lambda v: v.isoformat()}


class WatchlistItemBase(BaseModel):
    symbol: str = Field(..., description="股票代码")
    name: str = Field(..., description="股票名称")


class WatchlistItemCreate(WatchlistItemBase):
    """创建观察列表项目的模型"""


class WatchlistItemResponse(WatchlistItemBase):
    id: int
    user_id: int
    created_at: datetime

    class Config:
        orm_mode = True


# 增强版API响应模型
class StockInfo(BaseModel):
    """股票信息模型"""
    symbol: str = Field(..., description="股票代码")
    name: str = Field(..., description="股票名称")
    market: str = Field(..., description="市场代码，SH或SZ")
    industry: Optional[str] = Field(None, description="所属行业")
    list_date: Optional[str] = Field(None, description="上市日期")


class Pagination(BaseModel):
    """分页信息模型"""
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页数量")
    total: int = Field(..., description="总记录数")
    pages: int = Field(..., description="总页数")


class StockListResponse(BaseModel):
    """股票列表响应模型"""
    code: int = Field(200, description="响应代码")
    message: str = Field("success", description="响应消息")
    data: Dict[str, Any] = Field(..., description="响应数据")


class Quote(BaseModel):
    """行情数据模型"""
    symbol: str = Field(..., description="股票代码")
    price: float = Field(..., description="最新价")
    change: float = Field(..., description="涨跌额")
    change_pct: float = Field(..., description="涨跌幅(%)")
    volume: int = Field(..., description="成交量")
    turnover: float = Field(..., description="成交额")
    high: float = Field(..., description="最高价")
    low: float = Field(..., description="最低价")
    open: float = Field(..., description="开盘价")
    close: float = Field(..., description="昨收价")
    timestamp: str = Field(..., description="更新时间")


class QuoteResponse(BaseModel):
    """行情响应模型"""
    code: int = Field(200, description="响应代码")
    message: str = Field("success", description="响应消息")
    data: Dict[str, Any] = Field(..., description="响应数据")


class KlineResponse(BaseModel):
    """K线响应模型"""
    code: int = Field(200, description="响应代码")
    message: str = Field("success", description="响应消息")
    data: Dict[str, Any] = Field(..., description="响应数据")


class SectorInfo(BaseModel):
    """板块信息模型"""
    code: str = Field(..., description="板块代码")
    name: str = Field(..., description="板块名称")
    stock_count: int = Field(..., description="股票数量")
    change_pct: float = Field(..., description="板块涨跌幅")


class SectorResponse(BaseModel):
    """板块响应模型"""
    code: int = Field(200, description="响应代码")
    message: str = Field("success", description="响应消息")
    data: Dict[str, Any] = Field(..., description="响应数据")


class SearchResult(BaseModel):
    """搜索结果模型"""
    symbol: str = Field(..., description="股票代码")
    name: str = Field(..., description="股票名称")
    market: str = Field(..., description="市场代码")
    industry: Optional[str] = Field(None, description="所属行业")


class SearchResponse(BaseModel):
    """搜索响应模型"""
    code: int = Field(200, description="响应代码")
    message: str = Field("success", description="响应消息")
    data: Dict[str, Any] = Field(..., description="响应数据")


class IndexInfo(BaseModel):
    """指数信息模型"""
    code: str = Field(..., description="指数代码")
    name: str = Field(..., description="指数名称")
    value: float = Field(..., description="指数值")
    change: float = Field(..., description="涨跌额")
    change_pct: float = Field(..., description="涨跌幅")


class MarketOverview(BaseModel):
    """市场概览模型"""
    total_stocks: int = Field(..., description="总股票数")
    trading_stocks: int = Field(..., description="交易股票数")
    gainers: int = Field(..., description="上涨股票数")
    losers: int = Field(..., description="下跌股票数")
    unchanged: int = Field(..., description="平盘股票数")
    total_volume: int = Field(..., description="总成交量")
    total_turnover: float = Field(..., description="总成交额")
    indices: Dict[str, Dict[str, float]] = Field(..., description="主要指数")


class MarketOverviewResponse(BaseModel):
    """市场概览响应模型"""
    code: int = Field(200, description="响应代码")
    message: str = Field("success", description="响应消息")
    data: Dict[str, Any] = Field(..., description="响应数据")


class RankingItem(BaseModel):
    """排行榜项目模型"""
    rank: int = Field(..., description="排名")
    symbol: str = Field(..., description="股票代码")
    name: str = Field(..., description="股票名称")
    price: float = Field(..., description="最新价")
    change_pct: float = Field(..., description="涨跌幅")
    volume: int = Field(..., description="成交量")
    turnover: float = Field(..., description="成交额")


class RankingResponse(BaseModel):
    """排行榜响应模型"""
    code: int = Field(200, description="响应代码")
    message: str = Field("success", description="响应消息")
    data: Dict[str, Any] = Field(..., description="响应数据")


class IndexResponse(BaseModel):
    """指数响应模型"""
    code: int = Field(200, description="响应代码")
    message: str = Field("success", description="响应消息")
    data: Dict[str, Any] = Field(..., description="响应数据")
