"""
市场数据WebSocket端点
提供实时行情数据推送服务
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, Set, Optional

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends
from fastapi.websockets import WebSocketState

# from app.core.websocket import WebSocketManager  # 暂时注释掉
from app.services.data_source_manager import data_source_manager
from app.core.dependencies import get_current_user_optional

logger = logging.getLogger(__name__)
router = APIRouter()

# WebSocket连接管理器
# ws_manager = WebSocketManager()  # 暂时注释掉

# 活跃的WebSocket连接
active_connections: Dict[str, WebSocket] = {}
# 订阅的股票代码
subscriptions: Dict[str, Set[str]] = {}  # client_id -> set of symbols


class MarketWebSocketManager:
    """市场数据WebSocket管理器"""
    
    def __init__(self):
        self.connections: Dict[str, WebSocket] = {}
        self.subscriptions: Dict[str, Set[str]] = {}
        self.push_task: Optional[asyncio.Task] = None
        self.is_running = False
    
    async def connect(self, websocket: WebSocket, client_id: str):
        """建立WebSocket连接"""
        await websocket.accept()
        self.connections[client_id] = websocket
        self.subscriptions[client_id] = set()
        logger.info(f"Market WebSocket client {client_id} connected")
        
        # 启动数据推送任务
        if not self.is_running:
            self.start_push_task()
    
    def disconnect(self, client_id: str):
        """断开WebSocket连接"""
        if client_id in self.connections:
            del self.connections[client_id]
        if client_id in self.subscriptions:
            del self.subscriptions[client_id]
        logger.info(f"Market WebSocket client {client_id} disconnected")
        
        # 如果没有连接了，停止推送任务
        if not self.connections and self.push_task:
            self.stop_push_task()
    
    async def subscribe(self, client_id: str, symbol: str):
        """订阅股票行情"""
        if client_id in self.subscriptions:
            self.subscriptions[client_id].add(symbol)
            logger.info(f"Client {client_id} subscribed to {symbol}")
            
            # 立即推送该股票的当前行情
            await self.push_quote(client_id, symbol)
    
    async def unsubscribe(self, client_id: str, symbol: str):
        """取消订阅股票行情"""
        if client_id in self.subscriptions:
            self.subscriptions[client_id].discard(symbol)
            logger.info(f"Client {client_id} unsubscribed from {symbol}")
    
    async def push_quote(self, client_id: str, symbol: str):
        """推送单个股票行情"""
        if client_id not in self.connections:
            return
        
        try:
            # 获取实时行情数据
            quote = await data_source_manager.get_realtime_quote(symbol)
            
            message = {
                "type": "quote",
                "data": {
                    "symbol": symbol,
                    "price": quote.get("price", 0),
                    "change": quote.get("change", 0),
                    "change_pct": quote.get("change_pct", 0),
                    "volume": quote.get("volume", 0),
                    "turnover": quote.get("turnover", 0),
                    "high": quote.get("high", 0),
                    "low": quote.get("low", 0),
                    "open": quote.get("open", 0),
                    "close": quote.get("close", 0),
                    "timestamp": quote.get("timestamp", datetime.now().isoformat())
                }
            }
            
            websocket = self.connections[client_id]
            if websocket.client_state == WebSocketState.CONNECTED:
                await websocket.send_text(json.dumps(message))
                
        except Exception as e:
            logger.error(f"Failed to push quote for {symbol} to client {client_id}: {e}")
    
    async def broadcast_quotes(self):
        """广播所有订阅的股票行情"""
        if not self.connections:
            return
        
        # 收集所有被订阅的股票代码
        all_symbols = set()
        for symbols in self.subscriptions.values():
            all_symbols.update(symbols)
        
        if not all_symbols:
            return
        
        try:
            # 批量获取行情数据
            quotes = await data_source_manager.get_batch_quotes(list(all_symbols))
            
            # 为每个连接推送其订阅的股票行情
            for client_id, symbols in self.subscriptions.items():
                if client_id not in self.connections:
                    continue
                
                websocket = self.connections[client_id]
                if websocket.client_state != WebSocketState.CONNECTED:
                    continue
                
                # 筛选该客户端订阅的股票
                client_quotes = [q for q in quotes if q.get("symbol") in symbols]
                
                if client_quotes:
                    message = {
                        "type": "quotes",
                        "data": client_quotes,
                        "timestamp": datetime.now().isoformat()
                    }
                    
                    try:
                        await websocket.send_text(json.dumps(message))
                    except Exception as e:
                        logger.error(f"Failed to send quotes to client {client_id}: {e}")
                        
        except Exception as e:
            logger.error(f"Failed to broadcast quotes: {e}")
    
    def start_push_task(self):
        """启动数据推送任务"""
        if not self.is_running:
            self.is_running = True
            self.push_task = asyncio.create_task(self._push_loop())
            logger.info("Market data push task started")
    
    def stop_push_task(self):
        """停止数据推送任务"""
        if self.push_task:
            self.push_task.cancel()
            self.push_task = None
        self.is_running = False
        logger.info("Market data push task stopped")
    
    async def _push_loop(self):
        """数据推送循环"""
        while self.is_running:
            try:
                await self.broadcast_quotes()
                await asyncio.sleep(5)  # 每5秒推送一次
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in push loop: {e}")
                await asyncio.sleep(10)  # 出错时等待10秒再重试


# 全局市场WebSocket管理器
market_ws_manager = MarketWebSocketManager()


@router.websocket("/ws/market")
async def websocket_market_endpoint(
    websocket: WebSocket,
    client_id: str = "default"
):
    """市场数据WebSocket端点"""
    await market_ws_manager.connect(websocket, client_id)
    
    try:
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            message = json.loads(data)
            
            message_type = message.get("type")
            
            if message_type == "subscribe":
                symbol = message.get("symbol")
                if symbol:
                    await market_ws_manager.subscribe(client_id, symbol)
            
            elif message_type == "unsubscribe":
                symbol = message.get("symbol")
                if symbol:
                    await market_ws_manager.unsubscribe(client_id, symbol)
            
            elif message_type == "ping":
                # 心跳响应
                await websocket.send_text(json.dumps({"type": "pong"}))
            
            else:
                logger.warning(f"Unknown message type: {message_type}")
                
    except WebSocketDisconnect:
        logger.info(f"Market WebSocket client {client_id} disconnected")
    except Exception as e:
        logger.error(f"Market WebSocket error for client {client_id}: {e}")
    finally:
        market_ws_manager.disconnect(client_id)


@router.get("/ws/market/status")
async def get_market_ws_status():
    """获取市场WebSocket状态"""
    return {
        "active_connections": len(market_ws_manager.connections),
        "total_subscriptions": sum(len(subs) for subs in market_ws_manager.subscriptions.values()),
        "is_running": market_ws_manager.is_running
    }
