"""
市场数据WebSocket端点
提供实时行情数据推送服务
"""

import asyncio
import json
import logging
import time
import random
from datetime import datetime
from typing import Dict, Set, Optional, Any, List
from decimal import Decimal
from collections import defaultdict, deque

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends
from fastapi.websockets import WebSocketState

# from app.core.websocket import WebSocketManager  # 暂时注释掉
from app.services.data_source_manager import data_source_manager
from app.core.dependencies import get_current_user_optional

logger = logging.getLogger(__name__)
router = APIRouter()

# WebSocket连接管理器
# ws_manager = WebSocketManager()  # 暂时注释掉

# 活跃的WebSocket连接
active_connections: Dict[str, WebSocket] = {}
# 订阅的股票代码
subscriptions: Dict[str, Set[str]] = {}  # client_id -> set of symbols


class MarketDataCache:
    """市场数据缓存，用于变化检测"""

    def __init__(self):
        self.data: Dict[str, Dict[str, Any]] = {}  # symbol -> latest_data
        self.last_update: Dict[str, float] = {}  # symbol -> timestamp

    def update(self, symbol: str, data: Dict[str, Any]) -> bool:
        """更新数据并返回是否有变化"""
        current_time = time.time()

        if symbol not in self.data:
            self.data[symbol] = data
            self.last_update[symbol] = current_time
            return True

        old_data = self.data[symbol]
        has_change = self._detect_change(old_data, data)

        if has_change:
            self.data[symbol] = data
            self.last_update[symbol] = current_time

        return has_change

    def _detect_change(self, old_data: Dict[str, Any], new_data: Dict[str, Any]) -> bool:
        """检测数据是否有显著变化"""
        # 价格变动检测（相对变动 > 0.05%）
        old_price = float(old_data.get("price", 0))
        new_price = float(new_data.get("price", 0))

        if old_price > 0:
            price_change_pct = abs(new_price - old_price) / old_price
            if price_change_pct > 0.0005:  # 0.05%
                return True

        # 成交量变化检测
        old_volume = int(old_data.get("volume", 0))
        new_volume = int(new_data.get("volume", 0))

        if new_volume != old_volume:
            return True

        return False

    def should_force_update(self, symbol: str, max_silence_seconds: float = 1.0) -> bool:
        """检查是否应该强制更新（心跳机制）"""
        if symbol not in self.last_update:
            return True

        return time.time() - self.last_update[symbol] > max_silence_seconds


class ActivityMonitor:
    """市场活跃度监控器"""

    def __init__(self, window_size: int = 60):
        self.window_size = window_size  # 监控窗口大小（秒）
        self.events: deque = deque()  # 事件时间戳队列

    def record_event(self):
        """记录一个变化事件"""
        current_time = time.time()
        self.events.append(current_time)

        # 清理过期事件
        cutoff_time = current_time - self.window_size
        while self.events and self.events[0] < cutoff_time:
            self.events.popleft()

    def get_activity_level(self) -> float:
        """获取当前活跃度（事件/秒）"""
        if not self.events:
            return 0.0

        return len(self.events) / self.window_size

    def get_adaptive_interval(self, min_interval: float = 0.2, max_interval: float = 3.0) -> float:
        """根据活跃度计算自适应推送间隔"""
        activity = self.get_activity_level()

        if activity == 0:
            return max_interval

        # 活跃度越高，间隔越短
        # 使用对数函数平滑过渡
        import math
        normalized_activity = min(activity / 10.0, 1.0)  # 假设10事件/秒为高活跃
        interval = max_interval - (max_interval - min_interval) * normalized_activity

        return max(min_interval, min(max_interval, interval))


class ConnectionQueue:
    """连接消息队列，实现背压控制"""

    def __init__(self, max_size: int = 100):
        self.max_size = max_size
        self.queue: deque = deque()
        self.dropped_count = 0

    def enqueue(self, message: Dict[str, Any]) -> bool:
        """入队消息，返回是否成功"""
        if len(self.queue) >= self.max_size:
            # 队列满时，丢弃最旧的消息，保留最新快照
            if self.queue:
                self.queue.popleft()
                self.dropped_count += 1

        self.queue.append(message)
        return True

    def dequeue_all(self) -> List[Dict[str, Any]]:
        """取出所有消息"""
        messages = list(self.queue)
        self.queue.clear()
        return messages

    def get_stats(self) -> Dict[str, int]:
        """获取队列统计信息"""
        return {
            "queue_size": len(self.queue),
            "dropped_count": self.dropped_count,
            "max_size": self.max_size
        }


class MarketWebSocketManager:
    """智能市场数据WebSocket管理器"""

    def __init__(self):
        self.connections: Dict[str, WebSocket] = {}
        self.subscriptions: Dict[str, Set[str]] = {}
        self.connection_queues: Dict[str, ConnectionQueue] = {}  # 每连接的消息队列
        self.push_task: Optional[asyncio.Task] = None
        self.is_running = False

        # 智能推送组件
        self.data_cache = MarketDataCache()
        self.activity_monitor = ActivityMonitor()

        # 推送配置
        self.min_push_interval = 0.2  # 最小推送间隔（秒）
        self.max_push_interval = 3.0  # 最大推送间隔（秒）
        self.heartbeat_interval = 1.0  # 心跳间隔（秒）
        self.queue_max_size = 100  # 每连接队列最大大小

        # 性能统计
        self.stats = {
            "total_pushes": 0,
            "change_driven_pushes": 0,
            "heartbeat_pushes": 0,
            "dropped_messages": 0,
            "last_push_time": 0
        }
    
    async def connect(self, websocket: WebSocket, client_id: str):
        """建立WebSocket连接"""
        await websocket.accept()
        self.connections[client_id] = websocket
        self.subscriptions[client_id] = set()
        self.connection_queues[client_id] = ConnectionQueue(self.queue_max_size)

        logger.info(f"Market WebSocket client {client_id} connected. Total: {len(self.connections)}")

        # 发送连接确认消息
        await self._send_to_client(client_id, {
            "type": "connected",
            "client_id": client_id,
            "timestamp": datetime.now().isoformat(),
            "config": {
                "min_push_interval": self.min_push_interval,
                "max_push_interval": self.max_push_interval,
                "heartbeat_interval": self.heartbeat_interval
            }
        })

        # 启动数据推送任务
        if not self.is_running:
            self.start_push_task()

    def disconnect(self, client_id: str):
        """断开WebSocket连接"""
        if client_id in self.connections:
            del self.connections[client_id]
        if client_id in self.subscriptions:
            del self.subscriptions[client_id]
        if client_id in self.connection_queues:
            # 记录丢弃的消息统计
            queue_stats = self.connection_queues[client_id].get_stats()
            self.stats["dropped_messages"] += queue_stats["dropped_count"]
            del self.connection_queues[client_id]

        logger.info(f"Market WebSocket client {client_id} disconnected. Remaining: {len(self.connections)}")

        # 如果没有连接了，停止推送任务
        if not self.connections and self.push_task:
            self.stop_push_task()
    
    async def subscribe(self, client_id: str, symbol: str):
        """订阅股票行情"""
        if client_id in self.subscriptions:
            self.subscriptions[client_id].add(symbol)
            logger.info(f"Client {client_id} subscribed to {symbol}")
            
            # 立即推送该股票的当前行情
            await self.push_quote(client_id, symbol)
    
    async def unsubscribe(self, client_id: str, symbol: str):
        """取消订阅股票行情"""
        if client_id in self.subscriptions:
            self.subscriptions[client_id].discard(symbol)
            logger.info(f"Client {client_id} unsubscribed from {symbol}")
    
    async def push_quote(self, client_id: str, symbol: str):
        """推送单个股票行情"""
        if client_id not in self.connections:
            return
        
        try:
            # 获取实时行情数据
            quote = await data_source_manager.get_realtime_quote(symbol)
            
            message = {
                "type": "quote",
                "data": {
                    "symbol": symbol,
                    "price": quote.get("price", 0),
                    "change": quote.get("change", 0),
                    "change_pct": quote.get("change_pct", 0),
                    "volume": quote.get("volume", 0),
                    "turnover": quote.get("turnover", 0),
                    "high": quote.get("high", 0),
                    "low": quote.get("low", 0),
                    "open": quote.get("open", 0),
                    "close": quote.get("close", 0),
                    "timestamp": quote.get("timestamp", datetime.now().isoformat())
                }
            }
            
            websocket = self.connections[client_id]
            if websocket.client_state == WebSocketState.CONNECTED:
                await websocket.send_text(json.dumps(message))
                
        except Exception as e:
            logger.error(f"Failed to push quote for {symbol} to client {client_id}: {e}")
    
    async def broadcast_quotes(self):
        """智能广播股票行情（变化驱动）"""
        if not self.connections:
            return

        # 收集所有被订阅的股票代码
        all_symbols = set()
        for symbols in self.subscriptions.values():
            all_symbols.update(symbols)

        if not all_symbols:
            return

        try:
            # 批量获取行情数据
            quotes = await data_source_manager.get_batch_quotes(list(all_symbols))

            # 检测变化并准备推送数据
            changed_symbols = set()
            heartbeat_symbols = set()

            for quote in quotes:
                symbol = quote.get("symbol")
                if not symbol:
                    continue

                # 检测数据变化
                has_change = self.data_cache.update(symbol, quote)
                should_heartbeat = self.data_cache.should_force_update(symbol, self.heartbeat_interval)

                if has_change:
                    changed_symbols.add(symbol)
                    self.activity_monitor.record_event()
                elif should_heartbeat:
                    heartbeat_symbols.add(symbol)

            # 只推送有变化或需要心跳的数据
            push_symbols = changed_symbols | heartbeat_symbols

            if not push_symbols:
                return

            # 为每个连接推送相关数据
            for client_id, subscribed_symbols in self.subscriptions.items():
                if client_id not in self.connections:
                    continue

                # 找出该客户端需要推送的股票
                client_push_symbols = push_symbols & subscribed_symbols
                if not client_push_symbols:
                    continue

                # 准备推送数据
                client_quotes = []
                for quote in quotes:
                    if quote.get("symbol") in client_push_symbols:
                        client_quotes.append(quote)

                if client_quotes:
                    # 区分变化推送和心跳推送
                    client_changed = changed_symbols & subscribed_symbols
                    client_heartbeat = heartbeat_symbols & subscribed_symbols

                    message = {
                        "type": "quotes",
                        "data": client_quotes,
                        "timestamp": datetime.now().isoformat(),
                        "meta": {
                            "changed_symbols": list(client_changed),
                            "heartbeat_symbols": list(client_heartbeat),
                            "total_symbols": len(client_quotes)
                        }
                    }

                    # 使用队列缓冲消息
                    queue = self.connection_queues.get(client_id)
                    if queue:
                        queue.enqueue(message)

            # 更新统计
            self.stats["total_pushes"] += 1
            if changed_symbols:
                self.stats["change_driven_pushes"] += 1
            if heartbeat_symbols:
                self.stats["heartbeat_pushes"] += 1
            self.stats["last_push_time"] = time.time()

        except Exception as e:
            logger.error(f"Failed to broadcast quotes: {e}")

    async def _send_to_client(self, client_id: str, message: Dict[str, Any]):
        """发送消息给指定客户端"""
        if client_id not in self.connections:
            return False

        websocket = self.connections[client_id]
        if websocket.client_state != WebSocketState.CONNECTED:
            return False

        try:
            await websocket.send_text(json.dumps(message))
            return True
        except Exception as e:
            logger.error(f"Failed to send message to client {client_id}: {e}")
            return False

    async def _flush_client_queue(self, client_id: str):
        """刷新客户端消息队列"""
        if client_id not in self.connection_queues:
            return

        queue = self.connection_queues[client_id]
        messages = queue.dequeue_all()

        if not messages:
            return

        # 合并多个消息（如果都是quotes类型）
        if len(messages) > 1 and all(msg.get("type") == "quotes" for msg in messages):
            # 合并所有quotes数据
            all_quotes = []
            all_changed = set()
            all_heartbeat = set()

            for msg in messages:
                all_quotes.extend(msg.get("data", []))
                meta = msg.get("meta", {})
                all_changed.update(meta.get("changed_symbols", []))
                all_heartbeat.update(meta.get("heartbeat_symbols", []))

            # 去重（保留最新数据）
            symbol_quotes = {}
            for quote in all_quotes:
                symbol = quote.get("symbol")
                if symbol:
                    symbol_quotes[symbol] = quote

            merged_message = {
                "type": "quotes",
                "data": list(symbol_quotes.values()),
                "timestamp": datetime.now().isoformat(),
                "meta": {
                    "changed_symbols": list(all_changed),
                    "heartbeat_symbols": list(all_heartbeat),
                    "total_symbols": len(symbol_quotes),
                    "merged_count": len(messages)
                }
            }

            await self._send_to_client(client_id, merged_message)
        else:
            # 发送所有消息
            for message in messages:
                await self._send_to_client(client_id, message)
    
    def start_push_task(self):
        """启动数据推送任务"""
        if not self.is_running:
            self.is_running = True
            self.push_task = asyncio.create_task(self._push_loop())
            logger.info("Market data push task started")
    
    def stop_push_task(self):
        """停止数据推送任务"""
        if self.push_task:
            self.push_task.cancel()
            self.push_task = None
        self.is_running = False
        logger.info("Market data push task stopped")
    
    async def _push_loop(self):
        """智能数据推送循环"""
        logger.info("Starting intelligent push loop")

        while self.is_running:
            try:
                loop_start = time.time()

                # 1. 检查并推送变化的数据
                await self.broadcast_quotes()

                # 2. 刷新所有客户端队列
                flush_tasks = []
                for client_id in list(self.connections.keys()):
                    flush_tasks.append(self._flush_client_queue(client_id))

                if flush_tasks:
                    await asyncio.gather(*flush_tasks, return_exceptions=True)

                # 3. 计算自适应推送间隔
                adaptive_interval = self.activity_monitor.get_adaptive_interval(
                    self.min_push_interval,
                    self.max_push_interval
                )

                # 4. 考虑处理时间，调整实际睡眠时间
                processing_time = time.time() - loop_start
                actual_sleep = max(0.05, adaptive_interval - processing_time)  # 最小50ms

                # 5. 记录性能指标
                if len(self.connections) > 0:
                    activity_level = self.activity_monitor.get_activity_level()
                    if self.stats["total_pushes"] % 100 == 0:  # 每100次推送记录一次
                        logger.info(
                            f"Push stats: total={self.stats['total_pushes']}, "
                            f"changed={self.stats['change_driven_pushes']}, "
                            f"heartbeat={self.stats['heartbeat_pushes']}, "
                            f"activity={activity_level:.2f}/s, "
                            f"interval={adaptive_interval:.3f}s, "
                            f"processing={processing_time:.3f}s"
                        )

                await asyncio.sleep(actual_sleep)

            except asyncio.CancelledError:
                logger.info("Push loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error in push loop: {e}")
                await asyncio.sleep(min(10, self.max_push_interval))  # 出错时等待，但不超过最大间隔

        logger.info("Push loop stopped")


# 全局市场WebSocket管理器
market_ws_manager = MarketWebSocketManager()


@router.websocket("/ws/market")
async def websocket_market_endpoint(
    websocket: WebSocket,
    client_id: str = "default"
):
    """市场数据WebSocket端点"""
    await market_ws_manager.connect(websocket, client_id)
    
    try:
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            message = json.loads(data)
            
            message_type = message.get("type")
            
            if message_type == "subscribe":
                symbol = message.get("symbol")
                if symbol:
                    await market_ws_manager.subscribe(client_id, symbol)
            
            elif message_type == "unsubscribe":
                symbol = message.get("symbol")
                if symbol:
                    await market_ws_manager.unsubscribe(client_id, symbol)
            
            elif message_type == "ping":
                # 心跳响应
                await websocket.send_text(json.dumps({"type": "pong"}))
            
            else:
                logger.warning(f"Unknown message type: {message_type}")
                
    except WebSocketDisconnect:
        logger.info(f"Market WebSocket client {client_id} disconnected")
    except Exception as e:
        logger.error(f"Market WebSocket error for client {client_id}: {e}")
    finally:
        market_ws_manager.disconnect(client_id)


@router.get("/ws/market/status")
async def get_market_ws_status():
    """获取市场WebSocket状态"""
    activity_level = market_ws_manager.activity_monitor.get_activity_level()
    adaptive_interval = market_ws_manager.activity_monitor.get_adaptive_interval(
        market_ws_manager.min_push_interval,
        market_ws_manager.max_push_interval
    )

    # 计算队列统计
    total_queue_size = 0
    total_dropped = 0
    for queue in market_ws_manager.connection_queues.values():
        stats = queue.get_stats()
        total_queue_size += stats["queue_size"]
        total_dropped += stats["dropped_count"]

    return {
        "active_connections": len(market_ws_manager.connections),
        "total_subscriptions": sum(len(subs) for subs in market_ws_manager.subscriptions.values()),
        "is_running": market_ws_manager.is_running,
        "performance": {
            "activity_level": round(activity_level, 3),
            "adaptive_interval": round(adaptive_interval, 3),
            "total_pushes": market_ws_manager.stats["total_pushes"],
            "change_driven_pushes": market_ws_manager.stats["change_driven_pushes"],
            "heartbeat_pushes": market_ws_manager.stats["heartbeat_pushes"],
            "last_push_time": market_ws_manager.stats["last_push_time"]
        },
        "queues": {
            "total_queue_size": total_queue_size,
            "total_dropped_messages": total_dropped + market_ws_manager.stats["dropped_messages"],
            "queue_max_size": market_ws_manager.queue_max_size
        },
        "config": {
            "min_push_interval": market_ws_manager.min_push_interval,
            "max_push_interval": market_ws_manager.max_push_interval,
            "heartbeat_interval": market_ws_manager.heartbeat_interval
        }
    }


@router.post("/ws/market/config")
async def update_market_ws_config(
    min_push_interval: Optional[float] = None,
    max_push_interval: Optional[float] = None,
    heartbeat_interval: Optional[float] = None,
    queue_max_size: Optional[int] = None
):
    """动态更新WebSocket配置"""
    updated = {}

    if min_push_interval is not None and 0.05 <= min_push_interval <= 1.0:
        market_ws_manager.min_push_interval = min_push_interval
        updated["min_push_interval"] = min_push_interval

    if max_push_interval is not None and 1.0 <= max_push_interval <= 10.0:
        market_ws_manager.max_push_interval = max_push_interval
        updated["max_push_interval"] = max_push_interval

    if heartbeat_interval is not None and 0.5 <= heartbeat_interval <= 5.0:
        market_ws_manager.heartbeat_interval = heartbeat_interval
        updated["heartbeat_interval"] = heartbeat_interval

    if queue_max_size is not None and 10 <= queue_max_size <= 1000:
        market_ws_manager.queue_max_size = queue_max_size
        # 更新现有队列的大小限制
        for queue in market_ws_manager.connection_queues.values():
            queue.max_size = queue_max_size
        updated["queue_max_size"] = queue_max_size

    logger.info(f"WebSocket config updated: {updated}")
    return {"updated": updated, "current_config": {
        "min_push_interval": market_ws_manager.min_push_interval,
        "max_push_interval": market_ws_manager.max_push_interval,
        "heartbeat_interval": market_ws_manager.heartbeat_interval,
        "queue_max_size": market_ws_manager.queue_max_size
    }}
