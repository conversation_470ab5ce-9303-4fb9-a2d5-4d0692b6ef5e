# Windows 10 环境配置说明

## 问题解决
原项目的虚拟环境是在macOS系统上创建的，包含了macOS特定的路径配置，在Windows 10系统上无法正常使用。

## 已完成的修复

### 1. 重新创建虚拟环境
- 删除了原有的macOS虚拟环境
- 使用Windows Python 3.10.13重新创建了虚拟环境 (推荐版本)
- 虚拟环境路径：`backend\venv` (注意：此目录不应提交到版本控制)

### 2. 安装依赖包
- 安装了所有主要依赖包，包括：
  - FastAPI、uvicorn（Web框架）
  - pandas、numpy、scipy（数据处理）
  - sqlalchemy（数据库）
  - TA-Lib（技术指标计算）
  - 其他量化交易相关包

### 3. 特殊处理
- **TA-Lib**: 使用了预编译的Windows wheel包，避免了编译问题
- **依赖版本**: 调整了部分包的版本以确保Windows兼容性

## 启动方式

### 方式1：使用批处理文件（推荐）
```cmd
backend\start_windows.bat
```

### 方式2：使用PowerShell脚本
```powershell
backend\start_windows.ps1
```

### 方式3：手动启动
```cmd
# 激活虚拟环境
backend\venv\Scripts\activate.bat

# 切换到backend目录
cd backend

# 启动服务
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

## 验证安装
运行以下命令验证主要依赖是否正确安装：
```cmd
backend\venv\Scripts\activate.bat
python -c "import fastapi, pandas, numpy, talib; print('所有主要依赖包导入成功！')"
```

## 注意事项
1. 确保系统已安装Python 3.10.13 (推荐版本)
2. 如需添加新的依赖包，请使用虚拟环境中的pip
3. 虚拟环境配置文件位于：`backend\venv\pyvenv.cfg`

## 文件说明
- `requirements-windows.txt`: Windows优化的依赖列表
- `start_windows.bat`: Windows批处理启动脚本
- `start_windows.ps1`: PowerShell启动脚本
- `README_WINDOWS.md`: 本说明文件
