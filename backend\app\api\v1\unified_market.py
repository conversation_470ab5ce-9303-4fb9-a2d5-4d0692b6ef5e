"""
统一市场数据API
整合所有市场相关的端点，解决路由重复和命名不一致问题
"""
from fastapi import APIRouter, Query, HTTPException, Depends
from typing import Optional, List, Dict, Any
from datetime import datetime

from app.core.unified_cache import unified_cache, CacheType
from app.services.historical_data_manager import historical_data_manager
from app.services.scheduler_service import scheduler_service
from app.core.auth import get_current_user
from app.db.models.user import User

router = APIRouter()


# ============ 实时行情接口 ============

@router.get("/quotes/realtime")
async def get_realtime_quotes(
    symbols: str = Query(..., description="股票代码，多个用逗号分隔")
):
    """
    获取实时行情
    
    - **symbols**: 股票代码列表，如"000001,000002,600036"
    """
    symbol_list = [s.strip() for s in symbols.split(",") if s.strip()]
    
    if not symbol_list:
        raise HTTPException(status_code=400, detail="请提供股票代码")
    
    # 尝试从缓存获取
    quotes = []
    for symbol in symbol_list:
        cached_quote = await unified_cache.get(CacheType.QUOTE, symbol)
        if cached_quote:
            quotes.append(cached_quote)
        else:
            # 生成模拟数据
            mock_quote = {
                "symbol": symbol,
                "name": f"股票{symbol}",
                "currentPrice": 10.0 + (hash(symbol) % 100) / 10,
                "change": (hash(symbol) % 200 - 100) / 100,
                "changePercent": (hash(symbol) % 2000 - 1000) / 100,
                "volume": hash(symbol) % 10000000,
                "timestamp": datetime.now().isoformat()
            }
            quotes.append(mock_quote)
            
            # 缓存数据
            await unified_cache.set(CacheType.QUOTE, mock_quote, symbol)
    
    return {
        "success": True,
        "data": quotes,
        "count": len(quotes),
        "timestamp": datetime.now().isoformat()
    }


@router.get("/quotes/{symbol}")
async def get_quote_detail(symbol: str):
    """
    获取单个股票详细行情
    
    - **symbol**: 股票代码
    """
    # 尝试从缓存获取
    cached_quote = await unified_cache.get(CacheType.QUOTE, symbol)
    if cached_quote:
        return {"success": True, "data": cached_quote}
    
    # 生成详细模拟数据
    base_price = 10.0 + (hash(symbol) % 100) / 10
    change = (hash(symbol) % 200 - 100) / 100
    
    quote = {
        "symbol": symbol,
        "name": f"股票{symbol}",
        "currentPrice": round(base_price + change, 2),
        "change": round(change, 2),
        "changePercent": round((change / base_price) * 100, 2),
        "openPrice": round(base_price, 2),
        "highPrice": round(base_price + abs(change) + 1, 2),
        "lowPrice": round(base_price - abs(change) - 1, 2),
        "prevClose": round(base_price, 2),
        "volume": hash(symbol) % 10000000,
        "amount": hash(symbol) % 1000000000,
        "turnoverRate": round((hash(symbol) % 1000) / 100, 2),
        "pe": round(15 + (hash(symbol) % 30), 2),
        "timestamp": datetime.now().isoformat()
    }
    
    # 缓存数据
    await unified_cache.set(CacheType.QUOTE, quote, symbol)
    
    return {"success": True, "data": quote}


# ============ K线数据接口 ============

@router.get("/kline/{symbol}")
async def get_kline_data(
    symbol: str,
    period: str = Query("1d", pattern="^(1m|5m|15m|30m|1h|1d|1w|1M)$"),
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    limit: int = Query(100, ge=1, le=1000)
):
    """
    获取K线数据
    
    - **symbol**: 股票代码
    - **period**: K线周期(1m/5m/15m/30m/1h/1d/1w/1M)
    - **start_date**: 开始日期
    - **end_date**: 结束日期
    - **limit**: 返回数量
    """
    # 构建缓存键
    cache_key_parts = [symbol, period, str(limit)]
    if start_date:
        cache_key_parts.append(start_date)
    if end_date:
        cache_key_parts.append(end_date)
    
    # 尝试从缓存获取
    cached_klines = await unified_cache.get(CacheType.KLINE, *cache_key_parts)
    if cached_klines:
        return {
            "success": True,
            "data": cached_klines,
            "symbol": symbol,
            "period": period,
            "count": len(cached_klines)
        }
    
    # 生成模拟K线数据
    klines = []
    base_price = 10.0 + (hash(symbol) % 100) / 10
    
    for i in range(limit):
        timestamp = datetime.now().timestamp() - (limit - i) * 86400  # 每天一根K线
        open_price = base_price + (hash(f"{symbol}_{i}_open") % 200 - 100) / 100
        close_price = open_price + (hash(f"{symbol}_{i}_close") % 200 - 100) / 100
        high_price = max(open_price, close_price) + (hash(f"{symbol}_{i}_high") % 100) / 100
        low_price = min(open_price, close_price) - (hash(f"{symbol}_{i}_low") % 100) / 100
        volume = hash(f"{symbol}_{i}_volume") % 10000000
        
        klines.append({
            "timestamp": int(timestamp),
            "open": round(open_price, 2),
            "high": round(high_price, 2),
            "low": round(low_price, 2),
            "close": round(close_price, 2),
            "volume": volume
        })
    
    # 缓存数据
    await unified_cache.set(CacheType.KLINE, klines, *cache_key_parts)
    
    return {
        "success": True,
        "data": klines,
        "symbol": symbol,
        "period": period,
        "count": len(klines)
    }


# ============ 历史数据接口 ============

@router.get("/historical/stocks")
async def get_historical_stock_list(
    market: Optional[str] = Query(None, description="市场代码 (SH/SZ/BJ)"),
    industry: Optional[str] = Query(None, description="行业分类"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(50, ge=1, le=200, description="每页数量"),
    current_user: User = Depends(get_current_user),
):
    """
    获取历史数据中的股票列表
    """
    await historical_data_manager.initialize()
    
    result = await historical_data_manager.get_stock_list(
        market=market,
        industry=industry,
        page=page,
        page_size=page_size
    )
    
    return {
        "success": True,
        "data": result,
        "timestamp": datetime.now().isoformat()
    }


@router.get("/historical/search")
async def search_historical_stocks(
    keyword: str = Query(..., min_length=1, description="搜索关键词"),
    limit: int = Query(20, ge=1, le=50, description="返回条数"),
    current_user: User = Depends(get_current_user),
):
    """
    在历史数据中搜索股票
    """
    await historical_data_manager.initialize()
    
    results = await historical_data_manager.search_stocks(keyword, limit)
    
    return {
        "success": True,
        "data": results,
        "count": len(results),
        "timestamp": datetime.now().isoformat()
    }


@router.get("/historical/data/{symbol}")
async def get_historical_stock_data(
    symbol: str,
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    columns: Optional[str] = Query(None, description="指定列名，逗号分隔"),
    current_user: User = Depends(get_current_user),
):
    """
    获取指定股票的历史数据
    """
    await historical_data_manager.initialize()
    
    column_list = columns.split(',') if columns else None
    
    df = await historical_data_manager.get_stock_data(
        symbol=symbol,
        start_date=start_date,
        end_date=end_date,
        columns=column_list
    )
    
    if df is None or df.empty:
        raise HTTPException(status_code=404, detail="未找到股票历史数据")
    
    # 转换为字典格式
    data = df.to_dict('records')
    
    return {
        "success": True,
        "data": data,
        "count": len(data),
        "columns": list(df.columns),
        "symbol": symbol,
        "timestamp": datetime.now().isoformat()
    }


# ============ 搜索接口 ============

@router.get("/search")
async def search_stocks(
    keyword: str = Query(..., min_length=1, description="搜索关键词"),
    limit: int = Query(20, ge=1, le=50, description="返回条数")
):
    """
    搜索股票（实时+历史）
    """
    # 尝试从缓存获取
    cache_key_parts = [keyword, str(limit)]
    cached_results = await unified_cache.get(CacheType.SEARCH, *cache_key_parts)
    if cached_results:
        return {
            "success": True,
            "data": cached_results,
            "count": len(cached_results),
            "timestamp": datetime.now().isoformat()
        }
    
    # 搜索历史数据
    try:
        await historical_data_manager.initialize()
        historical_results = await historical_data_manager.search_stocks(keyword, limit)
    except Exception as e:
        historical_results = []
    
    # 如果历史数据不足，补充模拟数据
    results = historical_results.copy()
    if len(results) < limit:
        for i in range(len(results), limit):
            results.append({
                "symbol": f"{keyword}{i:03d}",
                "name": f"{keyword}股票{i}",
                "market": "SH" if i % 2 == 0 else "SZ",
                "type": "stock"
            })
    
    # 缓存结果
    await unified_cache.set(CacheType.SEARCH, results, *cache_key_parts)
    
    return {
        "success": True,
        "data": results,
        "count": len(results),
        "timestamp": datetime.now().isoformat()
    }


# ============ 系统管理接口 ============

@router.post("/cache/clear")
async def clear_cache(
    pattern: Optional[str] = Query("*", description="清除模式"),
    current_user: User = Depends(get_current_user),
):
    """
    清除缓存
    """
    try:
        if pattern == "*":
            # 清除所有缓存
            total_cleared = 0
            for cache_type in CacheType:
                cleared = await unified_cache.clear(cache_type)
                total_cleared += cleared
        else:
            # 根据模式清除
            total_cleared = await unified_cache.clear()
        
        return {
            "success": True,
            "cleared_count": total_cleared,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清除缓存失败: {str(e)}")


@router.post("/historical/rebuild-index")
async def rebuild_historical_index(
    current_user: User = Depends(get_current_user),
):
    """
    重建历史数据索引
    """
    try:
        await historical_data_manager.initialize()
        result = await historical_data_manager.rebuild_index()
        
        return {
            "success": True,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重建索引失败: {str(e)}")


@router.get("/health")
async def health_check():
    """
    健康检查
    """
    try:
        # 检查缓存状态
        cache_stats = await unified_cache.get_stats()
        
        # 检查调度器状态
        scheduler_jobs = scheduler_service.get_jobs() if scheduler_service._initialized else []
        
        return {
            "success": True,
            "status": "healthy",
            "cache_stats": cache_stats,
            "scheduler_jobs_count": len(scheduler_jobs),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "success": False,
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
