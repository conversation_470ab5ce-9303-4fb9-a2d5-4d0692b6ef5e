"""
统一市场数据API
整合所有市场相关的端点，解决路由重复和命名不一致问题
"""
from fastapi import APIRouter, Query, HTTPException, Depends
from typing import Optional, List, Dict, Any
from datetime import datetime

from app.core.unified_cache import unified_cache, CacheType
from app.services.historical_data_manager import historical_data_manager
from app.services.scheduler_service import scheduler_service
from app.services.market_facade_service import market_facade
# 临时移除认证依赖，避免导入错误
# from app.core.auth import get_current_user
# from app.db.models.user import User

router = APIRouter()


# ============ 实时行情接口 ============

@router.get("/quotes/realtime")
async def get_realtime_quotes(
    symbols: str = Query(..., description="股票代码，多个用逗号分隔")
):
    """
    获取实时行情

    - **symbols**: 股票代码列表，如"000001,000002,600036"
    """
    symbol_list = [s.strip() for s in symbols.split(",") if s.strip()]

    if not symbol_list:
        raise HTTPException(status_code=400, detail="请提供股票代码")

    # 使用统一服务门面
    quotes = await market_facade.get_batch_quotes(symbol_list)

    # 转换为API响应格式
    quote_data = []
    for quote in quotes:
        quote_data.append({
            "symbol": quote.symbol,
            "name": quote.name,
            "currentPrice": quote.current_price,
            "change": quote.change,
            "changePercent": quote.change_percent,
            "volume": quote.volume,
            "timestamp": quote.timestamp.isoformat() if quote.timestamp else datetime.now().isoformat()
        })

    return {
        "success": True,
        "data": quote_data,
        "count": len(quote_data),
        "timestamp": datetime.now().isoformat()
    }


@router.get("/quotes/{symbol}")
async def get_quote_detail(symbol: str):
    """
    获取单个股票详细行情

    - **symbol**: 股票代码
    """
    # 使用统一服务门面
    quote = await market_facade.get_realtime_quote(symbol)

    if not quote:
        raise HTTPException(status_code=404, detail=f"未找到股票 {symbol} 的行情数据")

    # 转换为API响应格式
    quote_data = {
        "symbol": quote.symbol,
        "name": quote.name,
        "currentPrice": quote.current_price,
        "change": quote.change,
        "changePercent": quote.change_percent,
        "openPrice": quote.open_price,
        "highPrice": quote.high_price,
        "lowPrice": quote.low_price,
        "prevClose": quote.prev_close,
        "volume": quote.volume,
        "amount": quote.amount,
        "turnoverRate": quote.turnover_rate,
        "pe": quote.pe_ratio,
        "timestamp": quote.timestamp.isoformat() if quote.timestamp else datetime.now().isoformat()
    }

    return {"success": True, "data": quote_data}


# ============ K线数据接口 ============

@router.get("/kline/{symbol}")
async def get_kline_data(
    symbol: str,
    period: str = Query("1d", pattern="^(1m|5m|15m|30m|1h|1d|1w|1M)$"),
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    limit: int = Query(100, ge=1, le=1000)
):
    """
    获取K线数据

    - **symbol**: 股票代码
    - **period**: K线周期(1m/5m/15m/30m/1h/1d/1w/1M)
    - **start_date**: 开始日期
    - **end_date**: 结束日期
    - **limit**: 返回数量
    """
    # 解析日期参数
    start_dt = None
    end_dt = None
    if start_date:
        try:
            start_dt = datetime.fromisoformat(start_date)
        except ValueError:
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
    if end_date:
        try:
            end_dt = datetime.fromisoformat(end_date)
        except ValueError:
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')

    # 使用统一服务门面
    klines = await market_facade.get_kline_data(
        symbol=symbol,
        period=period,
        start_date=start_dt,
        end_date=end_dt,
        limit=limit
    )

    # 转换为API响应格式
    kline_data = []
    for kline in klines:
        kline_data.append({
            "timestamp": kline.timestamp,
            "open": kline.open,
            "high": kline.high,
            "low": kline.low,
            "close": kline.close,
            "volume": kline.volume,
            "amount": kline.amount
        })

    return {
        "success": True,
        "data": kline_data,
        "symbol": symbol,
        "period": period,
        "count": len(kline_data)
    }


# ============ 历史数据接口 ============

@router.get("/historical/stocks")
async def get_historical_stock_list(
    market: Optional[str] = Query(None, description="市场代码 (SH/SZ/BJ)"),
    industry: Optional[str] = Query(None, description="行业分类"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(50, ge=1, le=200, description="每页数量"),
    # current_user: User = Depends(get_current_user),  # 临时移除认证
):
    """
    获取历史数据中的股票列表
    """
    await historical_data_manager.initialize()
    
    result = await historical_data_manager.get_stock_list(
        market=market,
        industry=industry,
        page=page,
        page_size=page_size
    )
    
    return {
        "success": True,
        "data": result,
        "timestamp": datetime.now().isoformat()
    }


@router.get("/historical/search")
async def search_historical_stocks(
    keyword: str = Query(..., min_length=1, description="搜索关键词"),
    limit: int = Query(20, ge=1, le=50, description="返回条数"),
    # current_user: User = Depends(get_current_user),  # 临时移除认证
):
    """
    在历史数据中搜索股票
    """
    await historical_data_manager.initialize()
    
    results = await historical_data_manager.search_stocks(keyword, limit)
    
    return {
        "success": True,
        "data": results,
        "count": len(results),
        "timestamp": datetime.now().isoformat()
    }


@router.get("/historical/data/{symbol}")
async def get_historical_stock_data(
    symbol: str,
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    columns: Optional[str] = Query(None, description="指定列名，逗号分隔"),
    # current_user: User = Depends(get_current_user),  # 临时移除认证
):
    """
    获取指定股票的历史数据
    """
    await historical_data_manager.initialize()
    
    column_list = columns.split(',') if columns else None
    
    df = await historical_data_manager.get_stock_data(
        symbol=symbol,
        start_date=start_date,
        end_date=end_date,
        columns=column_list
    )
    
    if df is None or df.empty:
        raise HTTPException(status_code=404, detail="未找到股票历史数据")
    
    # 转换为字典格式
    data = df.to_dict('records')
    
    return {
        "success": True,
        "data": data,
        "count": len(data),
        "columns": list(df.columns),
        "symbol": symbol,
        "timestamp": datetime.now().isoformat()
    }


# ============ 搜索接口 ============

@router.get("/search")
async def search_stocks(
    keyword: str = Query(..., min_length=1, description="搜索关键词"),
    limit: int = Query(20, ge=1, le=50, description="返回条数")
):
    """
    搜索股票（实时+历史）
    """
    # 使用统一服务门面搜索
    results = await market_facade.search_stocks(keyword, limit)

    # 如果服务门面没有结果，尝试历史数据管理器
    if not results:
        try:
            await historical_data_manager.initialize()
            results = await historical_data_manager.search_stocks(keyword, limit)
        except Exception as e:
            logger.warning(f"Historical data search failed: {e}")
            results = []

    return {
        "success": True,
        "data": results,
        "count": len(results),
        "timestamp": datetime.now().isoformat()
    }


@router.get("/depth/{symbol}")
async def get_market_depth(symbol: str):
    """
    获取市场深度数据

    - **symbol**: 股票代码
    """
    # 使用统一服务门面
    depth_data = await market_facade.get_market_depth(symbol)

    if not depth_data:
        raise HTTPException(status_code=404, detail=f"未找到股票 {symbol} 的深度数据")

    return {
        "success": True,
        "data": depth_data,
        "symbol": symbol,
        "timestamp": datetime.now().isoformat()
    }


# ============ 系统管理接口 ============

@router.post("/cache/clear")
async def clear_cache(
    pattern: Optional[str] = Query("*", description="清除模式"),
    # current_user: User = Depends(get_current_user),  # 临时移除认证
):
    """
    清除缓存
    """
    try:
        if pattern == "*":
            # 清除所有缓存
            total_cleared = 0
            for cache_type in CacheType:
                cleared = await unified_cache.clear(cache_type)
                total_cleared += cleared
        else:
            # 根据模式清除
            total_cleared = await unified_cache.clear()
        
        return {
            "success": True,
            "cleared_count": total_cleared,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清除缓存失败: {str(e)}")


@router.post("/historical/rebuild-index")
async def rebuild_historical_index(
    # current_user: User = Depends(get_current_user),  # 临时移除认证
):
    """
    重建历史数据索引
    """
    try:
        await historical_data_manager.initialize()
        result = await historical_data_manager.rebuild_index()
        
        return {
            "success": True,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重建索引失败: {str(e)}")


@router.get("/health")
async def health_check():
    """
    健康检查
    """
    try:
        # 检查缓存状态
        cache_stats = await unified_cache.get_stats()
        
        # 检查调度器状态
        scheduler_jobs = scheduler_service.get_jobs() if scheduler_service._initialized else []
        
        return {
            "success": True,
            "status": "healthy",
            "cache_stats": cache_stats,
            "scheduler_jobs_count": len(scheduler_jobs),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "success": False,
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
