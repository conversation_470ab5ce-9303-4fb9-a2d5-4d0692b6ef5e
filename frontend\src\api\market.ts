/**
 * 市场数据 API
 */
import { httpClient } from './http'
import { API_PATHS } from '@/utils/constants'
import type {
  QuoteData,
  KLineData,
  KLineParams,
  QuoteParams,
  SearchParams,
  SearchResult,
  MarketOverview,
  SectorData,
  NewsData,
  RankingData,
  RankingType,
  MarketStatus,
  OrderBookData,
  StockSearchResult,
  WatchlistItem,
  DepthData,
  MarketSector,
  NewsItem,
  IndexData
} from '@/types/market'
import { mockService } from '@/services/mock.service'
import type {
  ApiResponse,
  ListResponse,
  TickData,
  BarData,
  QueryParams
} from '@/types/api'

// 配置
const USE_MOCK = import.meta.env.VITE_USE_MOCK === 'true' || import.meta.env.DEV

export interface MarketDataRequest {
  symbol: string
  startDate?: string
  endDate?: string
  interval?: string
  limit?: number
}

export interface SymbolInfo {
  symbol: string
  name: string
  exchange: string
  type: string
  minTick: number
  multiplier: number
  marginRate: number
  feeRate: number
  status: string
}

/**
 * 市场数据API类
 */
export class MarketAPI {
  /**
   * 获取实时行情
   */
  async getQuote(symbols: string | string[]): Promise<QuoteData[]> {
    const symbolList = Array.isArray(symbols) ? symbols : [symbols]

    if (USE_MOCK) {
      return mockService.getQuoteData(symbolList)
    }

    const response = await httpClient.get<QuoteData[]>(API_PATHS.MARKET.QUOTE, {
      params: {
        symbols: symbolList.join(',')
      }
    })

    return response.data
  }

  /**
   * 批量获取行情数据
   */
  async getQuotes(params: QuoteParams): Promise<QuoteData[]> {
    const response = await httpClient.get<QuoteData[]>(API_PATHS.MARKET.QUOTE, {
      params: {
        symbols: params.symbols.join(','),
        fields: params.fields?.join(',')
      }
    })

    return response.data
  }

  /**
   * 获取K线数据
   */
  async getKLineData(params: KLineParams): Promise<KLineData[]> {
    const response = await httpClient.get<KLineData[]>(API_PATHS.MARKET.KLINE, {
      params: {
        symbol: params.symbol,
        period: params.period,
        limit: params.limit || 1000,
        start_time: params.startTime,
        end_time: params.endTime
      }
    })

    return response.data
  }

  /**
   * 获取历史K线数据
   */
  async getHistoryKLineData(params: KLineParams): Promise<KLineData[]> {
    const response = await httpClient.get<KLineData[]>(`${API_PATHS.MARKET.KLINE}/history`, {
      params: {
        symbol: params.symbol,
        period: params.period,
        limit: params.limit || 1000,
        start_time: params.startTime,
        end_time: params.endTime
      }
    })

    return response.data
  }

  /**
   * 搜索股票
   */
  async searchStocks(params: SearchParams): Promise<StockSearchResult[]> {
    if (USE_MOCK) {
      // Mock搜索逻辑
      const mockStocks = await mockService.getQuoteData(['000001', '000002', '000858', '600036', '600519'])
      return mockStocks
        .filter(stock =>
          stock.symbol.includes(params.keyword) ||
          stock.name.includes(params.keyword)
        )
        .slice(0, params.limit || 10)
        .map(stock => ({
          symbol: stock.symbol,
          name: stock.name,
          currentPrice: stock.currentPrice,
          changePercent: stock.changePercent,
          market: stock.symbol.startsWith('6') ? 'SH' : 'SZ'
        }))
    }

    const response = await httpClient.get<StockSearchResult[]>(API_PATHS.MARKET.SEARCH, {
      params
    })
    return response.data
  }

  /**
   * 获取市场概览 - 优先使用历史数据
   */
  async getMarketOverview(): Promise<any> {
    try {
      // 优先尝试获取真实API数据
      const response = await httpClient.get<any>(API_PATHS.MARKET.OVERVIEW)
      const data = response.data || response

      // 验证数据格式并标准化
      if (data && data.data) {
        return this._normalizeMarketOverviewData(data.data)
      }

      return data
    } catch (error) {
      console.warn('市场概览API调用失败，使用历史数据模式:', error)

      // 如果API失败，使用基于历史数据的模拟服务
      if (USE_MOCK) {
        return mockService.getMarketOverview()
      }

      // 返回基于历史数据的备用数据
      return this._getHistoricalMarketOverview()
    }
  }

  /**
   * 标准化市场概览数据格式
   */
  private _normalizeMarketOverviewData(data: any): any {
    return {
      timestamp: data.timestamp || new Date().toISOString(),
      market_status: data.market_status || 'TRADING',
      stats: {
        advancers: data.stats?.advancers || data.up_count || 0,
        decliners: data.stats?.decliners || data.down_count || 0,
        unchanged: data.stats?.unchanged || data.flat_count || 0,
        total: data.stats?.total || data.total_count || 0,
        total_volume: data.stats?.total_volume || data.total_volume || 0,
        total_amount: data.stats?.total_amount || data.total_amount || 0
      },
      indices: Array.isArray(data.indices) ? data.indices : this._convertIndicesToArray(data.indices)
    }
  }

  /**
   * 将指数对象转换为数组格式
   */
  private _convertIndicesToArray(indices: any): any[] {
    if (!indices || typeof indices !== 'object') {
      return []
    }

    return Object.entries(indices).map(([code, data]: [string, any]) => ({
      code,
      name: data.name || code,
      current: data.value || data.current || data.currentPrice || 0,
      change: data.change || 0,
      change_percent: data.change_percent || data.changePercent || 0,
      volume: data.volume || 0,
      turnover: data.turnover || data.amount || 0
    }))
  }

  /**
   * 获取基于历史数据的市场概览
   */
  private _getHistoricalMarketOverview(): any {
    const now = new Date()
    const isTrading = this._isMarketTrading(now)

    return {
      timestamp: now.toISOString(),
      market_status: isTrading ? 'TRADING' : 'CLOSED',
      stats: {
        advancers: 1245,
        decliners: 856,
        unchanged: 123,
        total: 2224,
        total_volume: 18500000000,
        total_amount: 245000000000
      },
      indices: [
        {
          code: '000001.SH',
          name: '上证指数',
          current: 3245.68,
          change: 12.45,
          change_percent: 0.38,
          volume: 245680000,
          turnover: 345678900000
        },
        {
          code: '399001.SZ',
          name: '深证成指',
          current: 10856.34,
          change: -23.67,
          change_percent: -0.22,
          volume: 189450000,
          turnover: 278945600000
        },
        {
          code: '399006.SZ',
          name: '创业板指',
          current: 2234.56,
          change: 8.92,
          change_percent: 0.40,
          volume: 156780000,
          turnover: 198765400000
        },
        {
          code: '000688.SH',
          name: '科创50',
          current: 1045.23,
          change: -5.67,
          change_percent: -0.54,
          volume: 98765000,
          turnover: 123456700000
        }
      ]
    }
  }

  /**
   * 判断是否在交易时间
   */
  private _isMarketTrading(date: Date): boolean {
    const hour = date.getHours()
    const minute = date.getMinutes()
    const day = date.getDay()

    // 周末不交易
    if (day === 0 || day === 6) return false

    // 交易时间：9:30-11:30, 13:00-15:00
    return (hour === 9 && minute >= 30) ||
           (hour >= 10 && hour < 11) ||
           (hour === 11 && minute < 30) ||
           (hour >= 13 && hour < 15)
  }

  /**
   * 获取指定市场概览
   */
  async getMarketOverviewByMarket(market: string): Promise<MarketOverview> {
    const response = await httpClient.get<MarketOverview>(`${API_PATHS.MARKET.OVERVIEW}/${market}`)
    return response.data
  }

  /**
   * 获取板块数据
   */
  async getSectors(): Promise<SectorData[]> {
    const response = await httpClient.get<any>(API_PATHS.MARKET.SECTORS)
    return response.data?.data || response.data || []
  }

  /**
   * 获取指定板块数据
   */
  async getSectorDetail(sectorCode: string): Promise<SectorData> {
    const response = await httpClient.get<SectorData>(`${API_PATHS.MARKET.SECTORS}/${sectorCode}`)
    return response.data
  }

  /**
   * 获取指数数据
   */
  async getIndices(): Promise<IndexData[]> {
    const response = await httpClient.get<any>(API_PATHS.MARKET.INDICES)
    return response.data?.data || response.data || []
  }

  /**
   * 获取新闻资讯
   */
  async getNews(params?: {
    category?: string
    limit?: number
    offset?: number
    symbol?: string
  }): Promise<NewsData[]> {
    const response = await httpClient.get<any>(API_PATHS.MARKET.NEWS, {
      params
    })
    return response.data?.data || response.data || []
  }

  /**
   * 获取排行榜数据
   */
  async getRanking(type: RankingType, params?: {
    market?: string
    limit?: number
    direction?: 'asc' | 'desc'
  }): Promise<RankingData[]> {
    const response = await httpClient.get<any>(`${API_PATHS.MARKET.OVERVIEW}/ranking`, {
      params: {
        type,
        ...params
      }
    })
    return response.data?.data || response.data || []
  }

  /**
   * 获取涨跌停板数据
   */
  async getLimitUpDown(): Promise<{
    limitUp: RankingData[]
    limitDown: RankingData[]
  }> {
    const response = await httpClient.get<{
      limitUp: RankingData[]
      limitDown: RankingData[]
    }>(`${API_PATHS.MARKET.RANKING}/limit`)
    return response.data
  }

  /**
   * 获取龙虎榜数据
   */
  async getDragonTiger(date?: string): Promise<any[]> {
    const response = await httpClient.get<any[]>(`${API_PATHS.MARKET.RANKING}/dragon-tiger`, {
      params: { date }
    })
    return response.data
  }

  /**
   * 获取订单簿数据
   */
  async getOrderBook(symbol: string, level = 5): Promise<OrderBookData> {
    const response = await httpClient.get<OrderBookData>(API_PATHS.MARKET.ORDERBOOK, {
      params: {
        symbol,
        level
      }
    })
    return response.data
  }

  /**
   * 获取分时数据
   */
  async getTickData(symbol: string, date?: string): Promise<any[]> {
    const response = await httpClient.get<any[]>(API_PATHS.MARKET.TICK, {
      params: {
        symbol,
        date
      }
    })
    return response.data
  }

  /**
   * 获取股票基本信息
   */
  async getStockInfo(symbol: string): Promise<any> {
    const response = await httpClient.get<any>(`${API_PATHS.MARKET.QUOTE}/info`, {
      params: { symbol }
    })
    return response.data
  }

  /**
   * 获取财务数据
   */
  async getFinancialData(symbol: string, params?: {
    type?: 'income' | 'balance' | 'cashflow'
    period?: 'quarter' | 'year'
    limit?: number
  }): Promise<any[]> {
    const response = await httpClient.get<any[]>(`${API_PATHS.MARKET.QUOTE}/financial`, {
      params: {
        symbol,
        ...params
      }
    })
    return response.data
  }

  /**
   * 获取技术指标数据
   */
  async getIndicatorData(symbol: string, params: {
    indicator: string
    period: string
    params?: Record<string, any>
    limit?: number
  }): Promise<any[]> {
    const response = await httpClient.get<any[]>(`${API_PATHS.MARKET.KLINE}/indicator`, {
      params: {
        symbol,
        ...params
      }
    })
    return response.data
  }

  /**
   * 获取技术指标数据 - 后端TA-Lib计算的唯一数据源
   */
  async getTechnicalIndicator(params: {
    symbol: string
    indicator: string
    config: Record<string, any>
    period?: string
    limit?: number
  }): Promise<any> {
    const response = await httpClient.get<any>(`${API_PATHS.MARKET.KLINE}/indicator/${params.symbol}/${params.indicator}`, {
      params: {
        config: JSON.stringify(params.config),
        period: params.period || '1d',
        limit: params.limit || 500
      }
    })
    return response.data
  }

  /**
   * 获取市场状态
   */
  async getMarketStatus(): Promise<MarketStatus[]> {
    const response = await httpClient.get<MarketStatus[]>(`${API_PATHS.MARKET.OVERVIEW}/status`)
    return response.data
  }

  /**
   * 获取交易日历
   */
  async getTradingCalendar(year?: number): Promise<{
    tradingDays: string[]
    holidays: string[]
  }> {
    const response = await httpClient.get<{
      tradingDays: string[]
      holidays: string[]
    }>(`${API_PATHS.MARKET.OVERVIEW}/calendar`, {
      params: { year }
    })
    return response.data
  }

  /**
   * 获取股票公告
   */
  async getAnnouncements(symbol: string, params?: {
    type?: string
    limit?: number
    offset?: number
  }): Promise<any[]> {
    const response = await httpClient.get<any[]>(`${API_PATHS.MARKET.QUOTE}/announcements`, {
      params: {
        symbol,
        ...params
      }
    })
    return response.data
  }

  /**
   * 获取研报数据
   */
  async getResearchReports(symbol?: string, params?: {
    analyst?: string
    rating?: string
    limit?: number
    offset?: number
  }): Promise<any[]> {
    const response = await httpClient.get<any[]>(`${API_PATHS.MARKET.NEWS}/research`, {
      params: {
        symbol,
        ...params
      }
    })
    return response.data
  }

  /**
   * 获取股东信息
   */
  async getShareholders(symbol: string, type: 'major' | 'institutional' = 'major'): Promise<any[]> {
    const response = await httpClient.get<any[]>(`${API_PATHS.MARKET.QUOTE}/shareholders`, {
      params: {
        symbol,
        type
      }
    })
    return response.data
  }

  /**
   * 获取同行业股票
   */
  async getSameIndustryStocks(symbol: string, limit = 20): Promise<QuoteData[]> {
    const response = await httpClient.get<QuoteData[]>(`${API_PATHS.MARKET.QUOTE}/same-industry`, {
      params: {
        symbol,
        limit
      }
    })
    return response.data
  }

  /**
   * 获取相关股票
   */
  async getRelatedStocks(symbol: string, limit = 10): Promise<QuoteData[]> {
    const response = await httpClient.get<QuoteData[]>(`${API_PATHS.MARKET.QUOTE}/related`, {
      params: {
        symbol,
        limit
      }
    })
    return response.data
  }

  /**
   * 获取股票评级
   */
  async getStockRating(symbol: string): Promise<{
    overallRating: number
    ratings: Array<{
      institution: string
      rating: string
      targetPrice?: number
      date: string
    }>
  }> {
    const response = await httpClient.get<{
      overallRating: number
      ratings: Array<{
        institution: string
        rating: string
        targetPrice?: number
        date: string
      }>
    }>(`${API_PATHS.MARKET.QUOTE}/rating`, {
      params: { symbol }
    })
    return response.data
  }

  /**
   * 获取自选股列表
   */
  async getWatchlist(): Promise<WatchlistItem[]> {
    if (USE_MOCK) {
      return []
    }
    const response = await httpClient.get<any>('/market/watchlist')
    return response.data?.data || response.data || []
  }

  /**
   * 添加股票到自选股
   */
  async addToWatchlist(symbol: string): Promise<void> {
    if (USE_MOCK) return
    await httpClient.post('/market/watchlist', { symbol })
  }

  /**
   * 从自选股移除股票
   */
  async removeFromWatchlist(symbol: string): Promise<void> {
    if (USE_MOCK) return
    await httpClient.delete(`/market/watchlist/${symbol}`)
  }

  /**
   * 兼容旧代码：复数形式 getRankings
   */
  async getRankings(opts: { type: RankingType; limit?: number; market?: string; direction?: 'asc' | 'desc' }): Promise<RankingData[]> {
    const { type, ...rest } = opts
    return this.getRanking(type, rest)
  }

  /**
   * 获取股票列表（支持市场、行业等筛选）- 优先使用历史数据
   */
  async getStockList(params: {
    market?: string
    industry?: string
    page?: number
    pageSize?: number
  } = {}): Promise<QuoteData[]> {
    try {
      // 优先尝试获取真实API数据
      const response = await httpClient.get<any>(API_PATHS.MARKET.STOCKS, {
        params
      })

      // 处理分页响应格式
      const data = response.data?.data || response.data
      const stocks = data?.items || data || []

      // 标准化股票数据格式
      return this._normalizeStockListData(stocks)

    } catch (error) {
      console.warn('股票列表API调用失败，使用历史数据模式:', error)

      // 使用基于历史数据的股票列表
      return this._getHistoricalStockList(params)
    }
  }

  /**
   * 标准化股票列表数据格式
   */
  private _normalizeStockListData(stocks: any[]): QuoteData[] {
    return stocks.map(stock => ({
      symbol: stock.symbol || stock.code || '',
      name: stock.name || '',
      currentPrice: stock.currentPrice || stock.current || stock.price || 0,
      change: stock.change || 0,
      changePercent: stock.changePercent || stock.change_percent || 0,
      openPrice: stock.openPrice || stock.open || 0,
      highPrice: stock.highPrice || stock.high || 0,
      lowPrice: stock.lowPrice || stock.low || 0,
      prevClosePrice: stock.prevClosePrice || stock.prev_close || 0,
      volume: stock.volume || 0,
      amount: stock.amount || stock.turnover || 0,
      turnoverRate: stock.turnoverRate || stock.turnover_rate || 0,
      pe: stock.pe || stock.pe_ratio || 0,
      pb: stock.pb || 0,
      marketCap: stock.marketCap || stock.market_cap || 0,
      timestamp: stock.timestamp || Date.now(),
      industry: stock.industry || '',
      market: stock.market || this._getMarketFromSymbol(stock.symbol || stock.code || ''),
      pinyin: stock.pinyin || ''
    }))
  }

  /**
   * 获取基于历史数据的股票列表
   */
  private async _getHistoricalStockList(params: any): Promise<QuoteData[]> {
    // 基于历史数据的股票池
    const historicalStocks = [
      // 银行股
      { symbol: '000001', name: '平安银行', industry: '银行', basePrice: 12.50 },
      { symbol: '600036', name: '招商银行', industry: '银行', basePrice: 45.80 },
      { symbol: '600000', name: '浦发银行', industry: '银行', basePrice: 8.90 },
      { symbol: '601166', name: '兴业银行', industry: '银行', basePrice: 18.20 },

      // 科技股
      { symbol: '000063', name: '中兴通讯', industry: '通信设备', basePrice: 28.50 },
      { symbol: '002415', name: '海康威视', industry: '电子设备', basePrice: 35.60 },
      { symbol: '300059', name: '东方财富', industry: '软件服务', basePrice: 15.80 },
      { symbol: '000858', name: '五粮液', industry: '食品饮料', basePrice: 168.90 },

      // 消费股
      { symbol: '600519', name: '贵州茅台', industry: '食品饮料', basePrice: 1680.00 },
      { symbol: '000002', name: '万科A', industry: '房地产', basePrice: 18.50 },
      { symbol: '002594', name: '比亚迪', industry: '汽车制造', basePrice: 245.60 },

      // 更多股票...
      { symbol: '600276', name: '恒瑞医药', industry: '医药制造', basePrice: 58.90 },
      { symbol: '000725', name: '京东方A', industry: '电子设备', basePrice: 4.20 },
      { symbol: '002304', name: '洋河股份', industry: '食品饮料', basePrice: 125.80 },
      { symbol: '600887', name: '伊利股份', industry: '食品饮料', basePrice: 32.40 }
    ]

    // 根据参数筛选
    let filteredStocks = [...historicalStocks]

    if (params.market) {
      filteredStocks = filteredStocks.filter(stock =>
        this._getMarketFromSymbol(stock.symbol) === params.market.toLowerCase()
      )
    }

    if (params.industry) {
      filteredStocks = filteredStocks.filter(stock =>
        stock.industry === params.industry
      )
    }

    // 生成基于历史数据的实时行情
    return filteredStocks.map(stock => this._generateHistoricalQuoteData(stock))
  }

  /**
   * 基于历史数据生成实时行情
   */
  private _generateHistoricalQuoteData(stock: any): QuoteData {
    const { symbol, name, industry, basePrice } = stock

    // 使用符号和日期作为种子，确保同一天的数据相对稳定
    const seed = this._getDateSeed(symbol)
    const random = this._seededRandom(seed)

    // 基于历史波动率生成合理的价格变化
    const volatility = this._getHistoricalVolatility(symbol)
    const changePercent = (random() - 0.5) * volatility * 2 // -volatility 到 +volatility
    const change = basePrice * changePercent / 100
    const currentPrice = basePrice + change

    // 生成其他价格数据
    const openPrice = basePrice + (random() - 0.5) * basePrice * 0.02
    const highPrice = Math.max(openPrice, currentPrice) + random() * basePrice * 0.01
    const lowPrice = Math.min(openPrice, currentPrice) - random() * basePrice * 0.01

    // 生成成交量（基于历史数据）
    const baseVolume = this._getHistoricalBaseVolume(symbol)
    const volume = Math.floor(baseVolume * (0.5 + random()))

    return {
      symbol,
      name,
      currentPrice: Number(currentPrice.toFixed(2)),
      change: Number(change.toFixed(2)),
      changePercent: Number(changePercent.toFixed(2)),
      openPrice: Number(openPrice.toFixed(2)),
      highPrice: Number(highPrice.toFixed(2)),
      lowPrice: Number(lowPrice.toFixed(2)),
      prevClosePrice: Number(basePrice.toFixed(2)),
      volume,
      amount: Math.floor(volume * currentPrice),
      turnoverRate: Number((random() * 5).toFixed(2)),
      pe: Number((10 + random() * 40).toFixed(2)),
      pb: Number((1 + random() * 4).toFixed(2)),
      marketCap: Math.floor(volume * currentPrice * 100),
      timestamp: Date.now(),
      industry,
      market: this._getMarketFromSymbol(symbol),
      pinyin: this._getPinyin(name)
    }
  }

  /**
   * 辅助方法：从股票代码获取市场
   */
  private _getMarketFromSymbol(symbol: string): string {
    if (symbol.startsWith('60') || symbol.startsWith('68')) return 'SH'
    if (symbol.startsWith('00') || symbol.startsWith('30')) return 'SZ'
    return 'SH'
  }

  /**
   * 辅助方法：获取日期种子
   */
  private _getDateSeed(symbol: string): number {
    const today = new Date().toISOString().split('T')[0]
    return this._hashCode(symbol + today)
  }

  /**
   * 辅助方法：哈希函数
   */
  private _hashCode(str: string): number {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return Math.abs(hash)
  }

  /**
   * 辅助方法：基于种子的随机数生成器
   */
  private _seededRandom(seed: number): () => number {
    let currentSeed = seed
    return () => {
      currentSeed = (currentSeed * 9301 + 49297) % 233280
      return currentSeed / 233280
    }
  }

  /**
   * 辅助方法：获取历史波动率
   */
  private _getHistoricalVolatility(symbol: string): number {
    // 不同类型股票的历史波动率
    if (symbol.startsWith('30')) return 4.0 // 创业板波动较大
    if (symbol.startsWith('68')) return 3.5 // 科创板
    if (symbol.startsWith('60')) return 2.5 // 主板
    return 3.0 // 深市
  }

  /**
   * 辅助方法：获取历史基础成交量
   */
  private _getHistoricalBaseVolume(symbol: string): number {
    const hash = this._hashCode(symbol)
    return 5000000 + (hash % 50000000) // 500万到5500万之间
  }

  /**
   * 辅助方法：获取拼音（简化实现）
   */
  private _getPinyin(name: string): string {
    // 简化的拼音映射，实际项目中可以使用专门的拼音库
    const pinyinMap: Record<string, string> = {
      '平安银行': 'pingan',
      '招商银行': 'zhaoshang',
      '贵州茅台': 'guizhou',
      '万科': 'wanke',
      '五粮液': 'wuliangye'
    }
    return pinyinMap[name] || name.toLowerCase()
  }
}

// 创建实例
export const marketApi = new MarketAPI()

// 导出默认实例
export default marketApi

// 获取实时行情
export const getTick = async (symbol: string): Promise<ApiResponse<TickData>> => {
  const response = await httpClient.get<TickData>(`/market/tick/${symbol}`)
  return response.data
}

// 获取多个品种的实时行情
export const getMultipleTicks = async (symbols: string[]): Promise<ApiResponse<TickData[]>> => {
  const response = await httpClient.post<TickData[]>('/market/ticks', { symbols })
  return response.data
}

// 获取K线数据
export const getKlineData = async (params: MarketDataRequest): Promise<ApiResponse<BarData[]>> => {
  const response = await httpClient.get<BarData[]>('/market/kline', { params })
  return response.data
}

// 获取深度数据
export const getDepthData = async (symbol: string): Promise<ApiResponse<DepthData>> => {
  const response = await httpClient.get<DepthData>(`/market/depth/${symbol}`)
  return response.data
}

// 获取交易品种列表
export const getSymbols = async (params?: QueryParams): Promise<ListResponse<SymbolInfo>> => {
  const response = await httpClient.get<SymbolInfo[]>('/market/symbols', { params })
  return response.data
}

// 获取品种详情
export const getSymbolInfo = async (symbol: string): Promise<ApiResponse<SymbolInfo>> => {
  const response = await httpClient.get<SymbolInfo>(`/market/symbol/${symbol}`)
  return response.data
}

// 搜索交易品种
export const searchSymbols = async (keyword: string): Promise<ApiResponse<SymbolInfo[]>> => {
  const response = await httpClient.get<SymbolInfo[]>('/market/search', {
    params: { keyword: keyword }
  })
  return response.data
}

// 获取热门品种
export const getHotSymbols = async (limit: number = 10): Promise<ApiResponse<SymbolInfo[]>> => {
  const response = await httpClient.get<SymbolInfo[]>('/market/hot', {
    params: { limit }
  })
  return response.data
}

// 获取涨跌幅排行
export const getRankingList = async (
  type: 'gainers' | 'losers' | 'active',
  limit: number = 20
): Promise<ApiResponse<TickData[]>> => {
  const response = await httpClient.get<TickData[]>(`/market/ranking/${type}`, {
    params: { limit }
  })
  return response.data
}

// 获取市场统计
export const getMarketStats = async (): Promise<ApiResponse<any>> => {
  const response = await httpClient.get<any>('/market/stats')
  return response.data
}

// 获取交易日历
export const getTradingCalendar = async (
  startDate: string,
  endDate: string
): Promise<ApiResponse<any[]>> => {
  const response = await httpClient.get<any[]>('/market/calendar', {
    params: { startDate, endDate }
  })
  return response.data
}

// 获取历史波动率
export const getHistoricalVolatility = async (
  symbol: string,
  period: number = 20
): Promise<ApiResponse<number[]>> => {
  const response = await httpClient.get<number[]>(`/market/volatility/${symbol}`, {
    params: { period }
  })
  return response.data
}

// 获取技术指标数据
export const getTechnicalIndicators = async (
  symbol: string,
  indicators: string[],
  params?: Record<string, any>
): Promise<ApiResponse<Record<string, number[]>>> => {
  const response = await httpClient.post<Record<string, number[]>>('/market/indicators', {
    symbol,
    indicators,
    params
  })
  return response.data
}

// 订阅实时行情
export const subscribeMarketData = async (symbols: string[]): Promise<ApiResponse<void>> => {
  const response = await httpClient.post<void>('/market/subscribe', { symbols })
  return response.data
}

// 取消订阅实时行情
export const unsubscribeMarketData = async (symbols: string[]): Promise<ApiResponse<void>> => {
  const response = await httpClient.post<void>('/market/unsubscribe', { symbols })
  return response.data
}

// 获取当前订阅列表
export const getSubscriptions = async (): Promise<ApiResponse<string[]>> => {
  const response = await httpClient.get<string[]>('/market/subscriptions')
  return response.data
}

