"""
响应优化中间件
用于优化API响应时间，减少超时错误
"""

import time
import asyncio
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import J<PERSON>NResponse

from app.core.logging_config import get_contextual_logger

logger = get_contextual_logger(__name__)


class ResponseOptimizationMiddleware(BaseHTTPMiddleware):
    """响应优化中间件"""

    def __init__(self, app):
        super().__init__(app)
        # 使用固定配置，避免参数传递问题
        self.max_response_time = 12.0  # 最大响应时间（秒）
        self.enable_compression = True
        self.enable_caching = True
        self.enable_timeout_protection = True
        
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求和响应"""
        start_time = time.time()
        
        try:
            # 如果启用超时保护，使用asyncio.wait_for
            if self.enable_timeout_protection:
                response = await asyncio.wait_for(
                    call_next(request),
                    timeout=self.max_response_time
                )
            else:
                response = await call_next(request)
                
            # 计算响应时间
            process_time = time.time() - start_time
            
            # 添加响应头
            response.headers["X-Process-Time"] = str(process_time)
            response.headers["X-Optimized"] = "true"
            
            # 如果响应时间过长，记录警告
            if process_time > 5.0:
                logger.warning(
                    f"慢响应检测: {request.method} {request.url.path} "
                    f"耗时 {process_time:.2f}s"
                )
            
            # 添加缓存头（对于GET请求）
            if self.enable_caching and request.method == "GET":
                if "Cache-Control" not in response.headers:
                    # 根据路径设置不同的缓存策略
                    if "/market/" in str(request.url.path):
                        response.headers["Cache-Control"] = "public, max-age=30"
                    elif "/api/v1/auth/" not in str(request.url.path):
                        response.headers["Cache-Control"] = "public, max-age=300"
            
            return response
            
        except asyncio.TimeoutError:
            # 处理超时
            logger.error(
                f"请求超时: {request.method} {request.url.path} "
                f"超过 {self.max_response_time}s"
            )
            return JSONResponse(
                status_code=408,
                content={
                    "error": "Request Timeout",
                    "message": f"请求处理时间超过 {self.max_response_time} 秒",
                    "code": "TIMEOUT_ERROR"
                }
            )
            
        except Exception as e:
            # 处理其他异常
            process_time = time.time() - start_time
            logger.error(
                f"响应处理异常: {request.method} {request.url.path} "
                f"耗时 {process_time:.2f}s, 错误: {str(e)}"
            )
            
            return JSONResponse(
                status_code=500,
                content={
                    "error": "Internal Server Error",
                    "message": "服务器内部错误",
                    "code": "INTERNAL_ERROR"
                }
            )


class FastResponseMiddleware(BaseHTTPMiddleware):
    """快速响应中间件 - 针对特定端点的优化"""
    
    def __init__(self, app):
        super().__init__(app)
        # 定义快速响应的端点
        self.fast_endpoints = {
            "/health",
            "/api/v1/health",
            "/api/v1/auth/me",
            "/api/cors/test",
            "/api/cors/debug"
        }
        
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求"""
        path = request.url.path
        
        # 对于快速端点，设置更短的超时时间
        if path in self.fast_endpoints:
            start_time = time.time()
            
            try:
                response = await asyncio.wait_for(
                    call_next(request),
                    timeout=2.0  # 快速端点2秒超时
                )
                
                process_time = time.time() - start_time
                response.headers["X-Fast-Response"] = "true"
                response.headers["X-Process-Time"] = str(process_time)
                
                return response
                
            except asyncio.TimeoutError:
                logger.warning(f"快速端点超时: {path}")
                return JSONResponse(
                    status_code=408,
                    content={
                        "error": "Fast Endpoint Timeout",
                        "message": "快速端点响应超时",
                        "path": path
                    }
                )
        
        # 其他端点正常处理
        return await call_next(request)


def setup_response_optimization(app):
    """设置响应优化中间件"""
    try:
        # 添加快速响应中间件
        app.add_middleware(FastResponseMiddleware)
        logger.info("快速响应中间件已添加")

        # 添加响应优化中间件（不传递额外参数）
        app.add_middleware(ResponseOptimizationMiddleware)
        logger.info("响应优化中间件已添加")

    except Exception as e:
        logger.error(f"响应优化中间件设置失败: {e}")
