"""
认证和授权相关的依赖注入函数
"""

from typing import Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTPBearer
from jose import JWTError, jwt
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.security import (
    verify_token,
    create_access_token,
    get_password_hash,
    verify_password,
)
from app.core.database import get_db
from app.db.models.user import User
from app.services.core.auth_service import AuthService

security = HTTPBearer()


async def authenticate_user(
    username: str, password: str, db: AsyncSession
) -> Optional[User]:
    """验证用户凭据"""
    auth_service = AuthService(db)

    # 通过用户名获取用户
    user = await auth_service.get_user_by_username(username)
    if not user:
        return None

    # 验证密码
    if not verify_password(password, user.hashed_password):
        return None

    return user


async def create_user(user_data: dict, db: AsyncSession = None) -> User:
    """创建新用户"""
    if db is None:
        async with get_db() as session:
            db = session

    auth_service = AuthService(db)

    # 检查用户名是否已存在
    existing_user = await auth_service.get_user_by_username(user_data["username"])
    if existing_user:
        raise ValueError("用户名已存在")

    # 检查邮箱是否已存在
    if user_data.get("email"):
        existing_email = await auth_service.get_user_by_email(user_data["email"])
        if existing_email:
            raise ValueError("邮箱已存在")

    # 创建用户
    user = await auth_service.create_user(user_data)
    return user


async def update_user_password(
    user_id: str, new_password: str, db: AsyncSession = None
) -> bool:
    """更新用户密码"""
    if db is None:
        async with get_db() as session:
            db = session

    auth_service = AuthService(db)

    # 获取用户
    user = await auth_service.get_user_by_id(user_id)
    if not user:
        return False

    # 更新密码
    hashed_password = get_password_hash(new_password)
    # 这里应该更新数据库中的密码
    # 暂时返回True，实际应该调用数据库更新方法
    return True


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
) -> User:
    """获取当前认证用户"""
    try:
        token = credentials.credentials

        # 开发环境特殊处理
        print(f"[DEBUG] Environment: {settings.ENVIRONMENT}, Token: {token}")
        if settings.ENVIRONMENT == "development" and token == "dev-token-for-testing":
            print("[DEBUG] Using development token, returning mock user")
            # 返回开发环境模拟用户
            from app.db.models.user import User
            mock_user = User()
            mock_user.id = 1
            mock_user.username = "dev_user"
            mock_user.email = "<EMAIL>"
            mock_user.is_active = True
            mock_user.is_admin = True
            return mock_user

        payload = verify_token(token)
        user_id: int = payload.get("sub")
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # 暂时返回一个模拟用户对象，避免数据库依赖
    from app.db.models.user import User

    mock_user = User()
    mock_user.id = user_id
    mock_user.username = f"user_{user_id}"
    mock_user.email = f"user_{user_id}@example.com"
    mock_user.is_active = True
    mock_user.is_admin = False
    return mock_user


async def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """获取当前活跃用户"""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Inactive user"
        )
    return current_user


async def get_current_admin_user(
    current_user: User = Depends(get_current_active_user),
) -> User:
    """获取当前管理员用户"""
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Not enough permissions"
        )
    return current_user


def get_optional_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(
        HTTPBearer(auto_error=False)
    ),
) -> Optional[User]:
    """获取可选的当前用户（用于公开接口）"""
    if not credentials:
        return None

    try:
        token = credentials.credentials

        # 开发环境特殊处理
        if settings.ENVIRONMENT == "development" and token == "dev-token-for-testing":
            from app.db.models.user import User
            mock_user = User()
            mock_user.id = 1
            mock_user.username = "dev_user"
            mock_user.email = "<EMAIL>"
            mock_user.is_active = True
            mock_user.is_admin = True
            return mock_user

        payload = verify_token(token)
        user_id: int = payload.get("sub")
        if user_id is None:
            return None

        # 这里应该查询数据库获取用户，暂时返回None
        return None
    except JWTError:
        return None


# 供其他中间件使用的便捷函数
def get_user_from_token(token: str) -> Optional[User]:
    """通过JWT令牌获取用户(简化版本，不查询数据库)"""
    from jose import JWTError

    payload = verify_token(token)
    if not payload:
        return None
    user_id = payload.get("sub")
    if user_id is None:
        return None
    # 构造模拟用户对象
    mock_user = User()
    mock_user.id = user_id
    mock_user.username = f"user_{user_id}"
    mock_user.email = f"user_{user_id}@example.com"
    mock_user.is_active = True
    mock_user.is_admin = False
    return mock_user
