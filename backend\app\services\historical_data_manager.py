"""
历史数据管理服务
负责历史数据的加载、索引、缓存和查询优化
集成统一缓存系统
"""

import os
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import asyncio
# import aiofiles  # 移除依赖，使用标准库
import pickle
from concurrent.futures import ThreadPoolExecutor
import sqlite3
import json

from app.core.unified_cache import unified_cache, CacheType

logger = logging.getLogger(__name__)


class HistoricalDataManager:
    """历史数据管理器"""
    
    def __init__(self):
        # 数据目录
        self.data_dir = Path(__file__).parent.parent.parent / "data" / "historical" / "stocks"
        self.index_dir = Path(__file__).parent.parent.parent / "data" / "index"
        self.index_dir.mkdir(exist_ok=True)
        
        # 索引文件
        self.stock_index_file = self.index_dir / "stock_index.db"
        self.metadata_file = self.index_dir / "metadata.json"
        
        # 缓存
        self._data_cache = {}
        self._index_cache = {}
        self._cache_size_limit = 100  # 最多缓存100个股票的数据
        
        # 线程池用于并发读取文件
        self._executor = ThreadPoolExecutor(max_workers=4)
        
        # 初始化标志
        self._initialized = False

    async def ensure_initialized(self):
        """确保管理器已初始化"""
        if not self._initialized:
            await self._initialize_index()
            self._initialized = True

    async def _initialize_index(self):
        """初始化数据索引"""
        try:
            if not self.stock_index_file.exists():
                await self._build_stock_index()
            else:
                await self._load_stock_index()
            logger.info("历史数据索引初始化完成")
        except Exception as e:
            logger.error(f"初始化历史数据索引失败: {e}")
    
    async def _build_stock_index(self):
        """构建股票数据索引"""
        logger.info("开始构建股票数据索引...")
        
        # 创建SQLite数据库
        conn = sqlite3.connect(self.stock_index_file)
        cursor = conn.cursor()
        
        # 创建索引表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS stock_index (
                symbol TEXT PRIMARY KEY,
                name TEXT,
                file_path TEXT,
                start_date TEXT,
                end_date TEXT,
                total_records INTEGER,
                file_size INTEGER,
                last_modified REAL,
                market TEXT,
                industry TEXT
            )
        ''')
        
        # 扫描所有CSV文件
        csv_files = list(self.data_dir.glob("*.csv"))
        total_files = len(csv_files)
        
        for i, csv_file in enumerate(csv_files):
            try:
                # 解析文件名
                filename = csv_file.name
                if '_' in filename:
                    symbol = filename.split('_')[0]
                    name = filename.split('_')[1].replace('.csv', '')
                else:
                    symbol = filename.replace('.csv', '')
                    name = f"股票{symbol}"
                
                # 读取文件基本信息
                file_stat = csv_file.stat()
                file_size = file_stat.st_size
                last_modified = file_stat.st_mtime
                
                # 快速读取日期范围
                df_sample = pd.read_csv(csv_file, nrows=1)
                df_tail = pd.read_csv(csv_file).tail(1)
                
                start_date = df_sample['日期'].iloc[0] if '日期' in df_sample.columns else None
                end_date = df_tail['日期'].iloc[0] if '日期' in df_tail.columns else None
                
                # 获取总记录数
                total_records = len(pd.read_csv(csv_file))
                
                # 判断市场和行业
                market = self._determine_market(symbol)
                industry = self._determine_industry(name)
                
                # 插入索引
                cursor.execute('''
                    INSERT OR REPLACE INTO stock_index 
                    (symbol, name, file_path, start_date, end_date, total_records, 
                     file_size, last_modified, market, industry)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (symbol, name, str(csv_file), start_date, end_date, 
                      total_records, file_size, last_modified, market, industry))
                
                if (i + 1) % 100 == 0:
                    logger.info(f"已处理 {i + 1}/{total_files} 个文件")
                    
            except Exception as e:
                logger.warning(f"处理文件 {csv_file} 失败: {e}")
        
        conn.commit()
        conn.close()
        
        # 保存元数据
        metadata = {
            'total_stocks': total_files,
            'index_built_time': datetime.now().isoformat(),
            'data_directory': str(self.data_dir)
        }
        
        with open(self.metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
        
        logger.info(f"股票数据索引构建完成，共处理 {total_files} 个文件")
    
    async def _load_stock_index(self):
        """加载股票索引到内存"""
        try:
            conn = sqlite3.connect(self.stock_index_file)
            cursor = conn.cursor()
            
            cursor.execute('SELECT * FROM stock_index')
            rows = cursor.fetchall()
            
            for row in rows:
                symbol = row[0]
                self._index_cache[symbol] = {
                    'name': row[1],
                    'file_path': row[2],
                    'start_date': row[3],
                    'end_date': row[4],
                    'total_records': row[5],
                    'file_size': row[6],
                    'last_modified': row[7],
                    'market': row[8],
                    'industry': row[9]
                }
            
            conn.close()
            logger.info(f"加载了 {len(self._index_cache)} 个股票的索引信息")
            
        except Exception as e:
            logger.error(f"加载股票索引失败: {e}")
    
    def _determine_market(self, symbol: str) -> str:
        """根据股票代码判断市场"""
        if symbol.startswith('00') or symbol.startswith('30'):
            return 'SZ'  # 深圳
        elif symbol.startswith('60') or symbol.startswith('68'):
            return 'SH'  # 上海
        elif symbol.startswith('8') or symbol.startswith('4'):
            return 'BJ'  # 北京
        else:
            return 'UNKNOWN'
    
    def _determine_industry(self, name: str) -> str:
        """根据股票名称简单判断行业"""
        industry_keywords = {
            '银行': ['银行', '农商', '城商'],
            '保险': ['保险', '人寿', '财险'],
            '证券': ['证券', '信托', '期货'],
            '房地产': ['地产', '房地产', '置业', '发展'],
            '医药': ['医药', '生物', '制药', '医疗'],
            '科技': ['科技', '软件', '信息', '数据', '网络'],
            '制造': ['制造', '机械', '设备', '工业'],
            '能源': ['能源', '石油', '煤炭', '电力'],
            '消费': ['消费', '零售', '商贸', '食品'],
            '交通': ['交通', '运输', '物流', '航空']
        }
        
        for industry, keywords in industry_keywords.items():
            if any(keyword in name for keyword in keywords):
                return industry
        
        return '其他'
    
    async def get_stock_data(
        self,
        symbol: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        columns: Optional[List[str]] = None
    ) -> Optional[pd.DataFrame]:
        """获取股票历史数据"""
        try:
            # 构建缓存键
            cache_key_parts = [
                symbol,
                start_date.strftime('%Y%m%d') if start_date else 'all',
                end_date.strftime('%Y%m%d') if end_date else 'all',
                '_'.join(columns) if columns else 'all'
            ]

            # 检查统一缓存
            cached_data = await unified_cache.get(CacheType.HISTORICAL, *cache_key_parts)
            if cached_data is not None:
                logger.debug(f"从统一缓存获取数据: {symbol}")
                # 将字典转换回DataFrame
                return pd.DataFrame(cached_data)

            # 检查本地缓存
            cache_key = f"{symbol}_{start_date}_{end_date}"
            if cache_key in self._data_cache:
                df = self._data_cache[cache_key]
                result_df = df[columns] if columns else df

                # 同时缓存到统一缓存
                try:
                    df_dict = result_df.to_dict('records')
                    await unified_cache.set(CacheType.HISTORICAL, df_dict, *cache_key_parts)
                except Exception as cache_error:
                    logger.warning(f"缓存数据失败: {cache_error}")

                return result_df

            # 检查索引
            if symbol not in self._index_cache:
                logger.warning(f"股票 {symbol} 不在索引中")
                return None

            file_path = self._index_cache[symbol]['file_path']

            # 异步读取文件
            loop = asyncio.get_event_loop()
            df = await loop.run_in_executor(
                self._executor,
                self._read_csv_file,
                file_path,
                start_date,
                end_date
            )

            if df is not None and not df.empty:
                # 缓存数据（控制缓存大小）
                if len(self._data_cache) >= self._cache_size_limit:
                    # 移除最旧的缓存项
                    oldest_key = next(iter(self._data_cache))
                    del self._data_cache[oldest_key]

                self._data_cache[cache_key] = df

                result_df = df[columns] if columns else df

                # 缓存到统一缓存系统
                try:
                    df_dict = result_df.to_dict('records')
                    await unified_cache.set(CacheType.HISTORICAL, df_dict, *cache_key_parts)
                except Exception as cache_error:
                    logger.warning(f"缓存数据失败: {cache_error}")

                return result_df

            return None

        except Exception as e:
            logger.error(f"获取股票 {symbol} 数据失败: {e}")
            return None
    
    def _read_csv_file(
        self,
        file_path: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Optional[pd.DataFrame]:
        """同步读取CSV文件"""
        try:
            df = pd.read_csv(file_path, encoding='utf-8')
            
            if df.empty:
                return None
            
            # 处理日期列
            if '日期' in df.columns:
                df['日期'] = pd.to_datetime(df['日期'])
                df = df.sort_values('日期')
                
                # 日期筛选
                if start_date:
                    df = df[df['日期'] >= start_date]
                if end_date:
                    df = df[df['日期'] <= end_date]
            
            return df
            
        except Exception as e:
            logger.error(f"读取文件 {file_path} 失败: {e}")
            return None
    
    async def get_stock_list(
        self,
        market: Optional[str] = None,
        industry: Optional[str] = None,
        page: int = 1,
        page_size: int = 50
    ) -> Dict[str, Any]:
        """获取股票列表"""
        try:
            # 筛选股票
            stocks = []
            for symbol, info in self._index_cache.items():
                if market and info.get('market') != market.upper():
                    continue
                if industry and info.get('industry') != industry:
                    continue
                
                stocks.append({
                    'symbol': symbol,
                    'name': info['name'],
                    'market': info['market'],
                    'industry': info['industry'],
                    'total_records': info['total_records'],
                    'start_date': info['start_date'],
                    'end_date': info['end_date']
                })
            
            # 分页
            total = len(stocks)
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            page_stocks = stocks[start_idx:end_idx]
            
            return {
                'stocks': page_stocks,
                'total': total,
                'page': page,
                'page_size': page_size
            }
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return {'stocks': [], 'total': 0, 'page': page, 'page_size': page_size}
    
    async def search_stocks(self, keyword: str, limit: int = 20) -> List[Dict[str, Any]]:
        """搜索股票"""
        try:
            results = []
            keyword_lower = keyword.lower()
            
            for symbol, info in self._index_cache.items():
                if (keyword_lower in symbol.lower() or
                    keyword_lower in info['name'].lower()):
                    results.append({
                        'symbol': symbol,
                        'name': info['name'],
                        'market': info['market'],
                        'industry': info['industry']
                    })
                    
                    if len(results) >= limit:
                        break
            
            return results
            
        except Exception as e:
            logger.error(f"搜索股票失败: {e}")
            return []
    
    async def get_market_statistics(self) -> Dict[str, Any]:
        """获取市场统计信息"""
        try:
            stats = {
                'total_stocks': len(self._index_cache),
                'markets': {},
                'industries': {},
                'data_coverage': {}
            }
            
            for symbol, info in self._index_cache.items():
                # 市场统计
                market = info.get('market', 'UNKNOWN')
                stats['markets'][market] = stats['markets'].get(market, 0) + 1
                
                # 行业统计
                industry = info.get('industry', '其他')
                stats['industries'][industry] = stats['industries'].get(industry, 0) + 1
            
            return stats
            
        except Exception as e:
            logger.error(f"获取市场统计失败: {e}")
            return {}
    
    async def clear_cache(self):
        """清除数据缓存"""
        self._data_cache.clear()
        logger.info("历史数据缓存已清除")
    
    async def rebuild_index(self):
        """重建索引"""
        logger.info("开始重建历史数据索引...")
        self._index_cache.clear()
        if self.stock_index_file.exists():
            self.stock_index_file.unlink()
        await self._build_stock_index()
        await self._load_stock_index()
        logger.info("历史数据索引重建完成")


# 创建全局实例
historical_data_manager = HistoricalDataManager()
