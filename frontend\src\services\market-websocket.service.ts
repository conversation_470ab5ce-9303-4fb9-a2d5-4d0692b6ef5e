/**
 * 市场数据WebSocket服务
 * 专门处理实时行情数据推送
 */

import { ElMessage } from 'element-plus'
import mitt from 'mitt'
import type { Emitter } from 'mitt'
import { websocketConfig } from '@/config/websocket'
import type { QuoteData } from '@/types/market'

export interface MarketWebSocketEvents {
  connected: void
  disconnected: void
  error: Event
  quote: QuoteData
  quotes: QuoteData[]
  subscribed: string
  unsubscribed: string
}

export interface MarketMessage {
  type: 'quote' | 'quotes' | 'subscribed' | 'unsubscribed' | 'error' | 'pong'
  data?: any
  symbol?: string
  message?: string
  timestamp?: string
}

export class MarketWebSocketService {
  private ws: WebSocket | null = null
  private eventEmitter: Emitter<MarketWebSocketEvents>
  private subscriptions = new Set<string>()
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private heartbeatInterval: NodeJS.Timeout | null = null
  private isConnecting = false
  private clientId: string

  constructor() {
    this.eventEmitter = mitt<MarketWebSocketEvents>()
    this.clientId = `market-client-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 连接WebSocket
   */
  async connect(): Promise<void> {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      return
    }

    this.isConnecting = true

    try {
      const wsUrl = `${websocketConfig.market}?client_id=${this.clientId}`
      console.log('Connecting to market WebSocket:', wsUrl)

      this.ws = new WebSocket(wsUrl)

      this.ws.onopen = () => {
        console.log('Market WebSocket connected')
        this.isConnecting = false
        this.reconnectAttempts = 0
        this.eventEmitter.emit('connected')
        this.startHeartbeat()
        
        // 重新订阅之前的股票
        this.resubscribeAll()
      }

      this.ws.onmessage = (event) => {
        try {
          const message: MarketMessage = JSON.parse(event.data)
          this.handleMessage(message)
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error)
        }
      }

      this.ws.onclose = (event) => {
        console.log('Market WebSocket disconnected:', event.code, event.reason)
        this.isConnecting = false
        this.stopHeartbeat()
        this.eventEmitter.emit('disconnected')
        
        // 自动重连
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.scheduleReconnect()
        } else {
          ElMessage.error('市场数据连接失败，请刷新页面重试')
        }
      }

      this.ws.onerror = (error) => {
        console.error('Market WebSocket error:', error)
        this.isConnecting = false
        this.eventEmitter.emit('error', error)
      }

    } catch (error) {
      this.isConnecting = false
      console.error('Failed to create WebSocket connection:', error)
      throw error
    }
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    this.stopHeartbeat()
    
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
    
    this.subscriptions.clear()
    this.reconnectAttempts = this.maxReconnectAttempts // 阻止自动重连
  }

  /**
   * 订阅股票行情
   */
  subscribe(symbol: string): void {
    if (!symbol) return

    this.subscriptions.add(symbol)

    if (this.isConnected()) {
      this.sendMessage({
        type: 'subscribe',
        symbol: symbol
      })
    }
  }

  /**
   * 取消订阅股票行情
   */
  unsubscribe(symbol: string): void {
    if (!symbol) return

    this.subscriptions.delete(symbol)

    if (this.isConnected()) {
      this.sendMessage({
        type: 'unsubscribe',
        symbol: symbol
      })
    }
  }

  /**
   * 批量订阅
   */
  subscribeMultiple(symbols: string[]): void {
    symbols.forEach(symbol => this.subscribe(symbol))
  }

  /**
   * 批量取消订阅
   */
  unsubscribeMultiple(symbols: string[]): void {
    symbols.forEach(symbol => this.unsubscribe(symbol))
  }

  /**
   * 获取当前订阅列表
   */
  getSubscriptions(): string[] {
    return Array.from(this.subscriptions)
  }

  /**
   * 检查是否已连接
   */
  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN
  }

  /**
   * 事件监听
   */
  on<K extends keyof MarketWebSocketEvents>(
    event: K,
    handler: (data: MarketWebSocketEvents[K]) => void
  ): void {
    this.eventEmitter.on(event, handler)
  }

  /**
   * 移除事件监听
   */
  off<K extends keyof MarketWebSocketEvents>(
    event: K,
    handler: (data: MarketWebSocketEvents[K]) => void
  ): void {
    this.eventEmitter.off(event, handler)
  }

  /**
   * 发送消息
   */
  private sendMessage(message: any): void {
    if (this.isConnected() && this.ws) {
      try {
        this.ws.send(JSON.stringify(message))
      } catch (error) {
        console.error('Failed to send WebSocket message:', error)
      }
    }
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(message: MarketMessage): void {
    switch (message.type) {
      case 'quote':
        if (message.data) {
          this.eventEmitter.emit('quote', message.data)
        }
        break

      case 'quotes':
        if (message.data && Array.isArray(message.data)) {
          this.eventEmitter.emit('quotes', message.data)
        }
        break

      case 'subscribed':
        if (message.symbol) {
          this.eventEmitter.emit('subscribed', message.symbol)
        }
        break

      case 'unsubscribed':
        if (message.symbol) {
          this.eventEmitter.emit('unsubscribed', message.symbol)
        }
        break

      case 'error':
        console.error('Market WebSocket error:', message.message)
        ElMessage.error(`行情数据错误: ${message.message}`)
        break

      case 'pong':
        // 心跳响应，不需要处理
        break

      default:
        console.warn('Unknown market message type:', message.type)
    }
  }

  /**
   * 重新订阅所有股票
   */
  private resubscribeAll(): void {
    this.subscriptions.forEach(symbol => {
      this.sendMessage({
        type: 'subscribe',
        symbol: symbol
      })
    })
  }

  /**
   * 计划重连
   */
  private scheduleReconnect(): void {
    this.reconnectAttempts++
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1)
    
    console.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`)
    
    setTimeout(() => {
      if (this.reconnectAttempts <= this.maxReconnectAttempts) {
        this.connect().catch(error => {
          console.error('Reconnect failed:', error)
        })
      }
    }, delay)
  }

  /**
   * 启动心跳
   */
  private startHeartbeat(): void {
    this.stopHeartbeat()
    
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected()) {
        this.sendMessage({ type: 'ping' })
      }
    }, 30000) // 每30秒发送一次心跳
  }

  /**
   * 停止心跳
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }
}

// 创建全局实例
export const marketWebSocketService = new MarketWebSocketService()

// 自动连接（可选）
if (typeof window !== 'undefined') {
  // 延迟连接，避免在页面加载时立即连接
  setTimeout(() => {
    marketWebSocketService.connect().catch(error => {
      console.warn('Failed to auto-connect to market WebSocket:', error)
    })
  }, 1000)
}
