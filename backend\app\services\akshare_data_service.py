"""
AKShare数据获取服务
使用AKShare库获取股票数据，作为Tushare的备用数据源
"""

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging
import json
from pathlib import Path
import concurrent.futures

# 尝试导入akshare
try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
except ImportError:
    AKSHARE_AVAILABLE = False
    logging.warning("AKShare库未安装，请运行: pip install akshare")

logger = logging.getLogger(__name__)

class AKShareDataService:
    """AKShare数据服务"""
    
    def __init__(self):
        self.cache_dir = Path("data/cache/akshare")
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=5)
        
    def _get_cache_path(self, cache_key: str, date: str = None) -> Path:
        """获取缓存文件路径"""
        if date is None:
            date = datetime.now().strftime('%Y%m%d')
        return self.cache_dir / f"{cache_key}_{date}.json"
    
    def _load_cache(self, cache_key: str, date: str = None) -> Optional[Dict]:
        """加载缓存数据"""
        cache_path = self._get_cache_path(cache_key, date)
        if cache_path.exists():
            try:
                with open(cache_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"加载AKShare缓存失败: {e}")
        return None
    
    def _save_cache(self, cache_key: str, data: Dict, date: str = None):
        """保存缓存数据"""
        cache_path = self._get_cache_path(cache_key, date)
        try:
            with open(cache_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.warning(f"保存AKShare缓存失败: {e}")
    
    async def _run_in_executor(self, func, *args, **kwargs):
        """在线程池中运行同步函数"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, func, *args, **kwargs)
    
    async def get_stock_info_a(self, use_cache: bool = True) -> List[Dict]:
        """获取A股股票信息"""
        if not AKSHARE_AVAILABLE:
            return []
        
        cache_key = "stock_info_a"
        
        if use_cache:
            cached_data = self._load_cache(cache_key)
            if cached_data:
                return cached_data.get('items', [])
        
        try:
            def fetch_data():
                df = ak.stock_info_a_code_name()
                return df.to_dict('records') if not df.empty else []
            
            data = await self._run_in_executor(fetch_data)
            
            if data and use_cache:
                self._save_cache(cache_key, {"items": data})
            
            return data
            
        except Exception as e:
            logger.error(f"AKShare获取股票信息失败: {e}")
            return []
    
    async def get_stock_zh_a_hist(self, symbol: str, period: str = "daily", 
                                  start_date: str = None, end_date: str = None,
                                  use_cache: bool = True) -> List[Dict]:
        """获取A股历史数据"""
        if not AKSHARE_AVAILABLE:
            return []
        
        if start_date is None:
            start_date = (datetime.now() - timedelta(days=300)).strftime('%Y%m%d')
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        cache_key = f"stock_hist_{symbol}_{period}_{start_date}_{end_date}"
        
        if use_cache:
            cached_data = self._load_cache(cache_key)
            if cached_data:
                return cached_data.get('items', [])
        
        try:
            def fetch_data():
                df = ak.stock_zh_a_hist(
                    symbol=symbol,
                    period=period,
                    start_date=start_date,
                    end_date=end_date,
                    adjust=""
                )
                return df.to_dict('records') if not df.empty else []
            
            data = await self._run_in_executor(fetch_data)
            
            if data and use_cache:
                self._save_cache(cache_key, {"items": data})
            
            return data
            
        except Exception as e:
            logger.error(f"AKShare获取{symbol}历史数据失败: {e}")
            return []
    
    async def get_stock_zh_a_spot_em(self, use_cache: bool = False) -> List[Dict]:
        """获取A股实时行情"""
        if not AKSHARE_AVAILABLE:
            logger.warning("AKShare不可用，返回模拟数据")
            return self._get_mock_realtime_data()

        cache_key = "stock_spot_em"

        if use_cache:
            cached_data = self._load_cache(cache_key)
            if cached_data:
                logger.info("从缓存加载实时行情数据")
                return cached_data.get('items', [])

        try:
            logger.info("尝试从AKShare获取实时行情数据")

            def fetch_data():
                # 设置较短超时，快速失败
                import requests
                session = requests.Session()
                session.timeout = 8  # 8秒超时

                # 临时替换requests的默认session
                original_get = requests.get
                requests.get = session.get

                try:
                    df = ak.stock_zh_a_spot_em()
                    return df.to_dict('records') if not df.empty else []
                finally:
                    # 恢复原始requests
                    requests.get = original_get

            # 使用asyncio.wait_for添加额外超时保护
            data = await asyncio.wait_for(
                self._run_in_executor(fetch_data),
                timeout=12.0
            )

            if data:
                if use_cache:
                    self._save_cache(cache_key, {"items": data})
                logger.info(f"成功获取{len(data)}只股票的实时行情")
                return data
            else:
                logger.warning("AKShare返回空数据，使用模拟数据")
                return self._get_mock_realtime_data()

        except asyncio.TimeoutError:
            logger.warning("AKShare API调用超时，使用模拟数据")
            return self._get_mock_realtime_data()
        except Exception as e:
            logger.error(f"AKShare获取实时行情失败: {e}")
            return self._get_mock_realtime_data()

    def _get_mock_realtime_data(self) -> List[Dict]:
        """获取模拟实时行情数据"""
        import random

        mock_stocks = [
            {"代码": "000001", "名称": "平安银行"},
            {"代码": "000002", "名称": "万科A"},
            {"代码": "000858", "名称": "五粮液"},
            {"代码": "600000", "名称": "浦发银行"},
            {"代码": "600036", "名称": "招商银行"},
            {"代码": "600519", "名称": "贵州茅台"},
            {"代码": "600887", "名称": "伊利股份"},
            {"代码": "000858", "名称": "五粮液"},
        ]

        result = []
        for stock in mock_stocks:
            base_price = random.uniform(10, 200)
            change_pct = random.uniform(-10, 10)
            change = base_price * change_pct / 100

            stock_data = {
                "代码": stock["代码"],
                "名称": stock["名称"],
                "最新价": round(base_price, 2),
                "涨跌额": round(change, 2),
                "涨跌幅": round(change_pct, 2),
                "今开": round(base_price * random.uniform(0.95, 1.05), 2),
                "最高": round(base_price * random.uniform(1.0, 1.1), 2),
                "最低": round(base_price * random.uniform(0.9, 1.0), 2),
                "昨收": round(base_price - change, 2),
                "成交量": random.randint(1000000, 100000000),
                "成交额": random.randint(100000000, 10000000000),
                "换手率": round(random.uniform(0.1, 5.0), 2),
                "市盈率-动态": round(random.uniform(10, 50), 2),
            }
            result.append(stock_data)

        logger.info(f"返回{len(result)}只股票的模拟实时行情数据")
        return result
    
    async def get_index_zh_a_hist(self, symbol: str, period: str = "daily",
                                  start_date: str = None, end_date: str = None,
                                  use_cache: bool = True) -> List[Dict]:
        """获取指数历史数据"""
        if not AKSHARE_AVAILABLE:
            return []
        
        if start_date is None:
            start_date = (datetime.now() - timedelta(days=300)).strftime('%Y%m%d')
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        cache_key = f"index_hist_{symbol}_{period}_{start_date}_{end_date}"
        
        if use_cache:
            cached_data = self._load_cache(cache_key)
            if cached_data:
                return cached_data.get('items', [])
        
        try:
            def fetch_data():
                df = ak.index_zh_a_hist(
                    symbol=symbol,
                    period=period,
                    start_date=start_date,
                    end_date=end_date
                )
                return df.to_dict('records') if not df.empty else []
            
            data = await self._run_in_executor(fetch_data)
            
            if data and use_cache:
                self._save_cache(cache_key, {"items": data})
            
            return data
            
        except Exception as e:
            logger.error(f"AKShare获取{symbol}指数数据失败: {e}")
            return []
    
    async def get_stock_individual_info_em(self, symbol: str, use_cache: bool = True) -> Dict:
        """获取个股信息"""
        if not AKSHARE_AVAILABLE:
            return {}
        
        cache_key = f"stock_info_{symbol}"
        
        if use_cache:
            cached_data = self._load_cache(cache_key)
            if cached_data:
                return cached_data.get('data', {})
        
        try:
            def fetch_data():
                df = ak.stock_individual_info_em(symbol=symbol)
                return df.to_dict('records')[0] if not df.empty else {}
            
            data = await self._run_in_executor(fetch_data)
            
            if data and use_cache:
                self._save_cache(cache_key, {"data": data})
            
            return data
            
        except Exception as e:
            logger.error(f"AKShare获取{symbol}个股信息失败: {e}")
            return {}
    
    def convert_to_standard_format(self, akshare_data: List[Dict], data_type: str = "daily") -> List[Dict]:
        """转换AKShare数据格式为标准格式"""
        if not akshare_data:
            return []
        
        if data_type == "daily":
            return [
                {
                    "timestamp": int(pd.to_datetime(item.get('日期', '')).timestamp() * 1000) if item.get('日期') else 0,
                    "open": float(item.get('开盘', 0)) if item.get('开盘') else 0,
                    "high": float(item.get('最高', 0)) if item.get('最高') else 0,
                    "low": float(item.get('最低', 0)) if item.get('最低') else 0,
                    "close": float(item.get('收盘', 0)) if item.get('收盘') else 0,
                    "volume": int(item.get('成交量', 0)) if item.get('成交量') else 0,
                    "amount": float(item.get('成交额', 0)) if item.get('成交额') else 0,
                    "change": float(item.get('涨跌额', 0)) if item.get('涨跌额') else 0,
                    "pct_change": float(item.get('涨跌幅', 0)) if item.get('涨跌幅') else 0
                }
                for item in akshare_data
                if item.get('日期')
            ]
        elif data_type == "realtime":
            return [
                {
                    "symbol": item.get('代码', ''),
                    "name": item.get('名称', ''),
                    "current_price": float(item.get('最新价', 0)) if item.get('最新价') else 0,
                    "change": float(item.get('涨跌额', 0)) if item.get('涨跌额') else 0,
                    "change_percent": float(item.get('涨跌幅', 0)) if item.get('涨跌幅') else 0,
                    "open_price": float(item.get('今开', 0)) if item.get('今开') else 0,
                    "high_price": float(item.get('最高', 0)) if item.get('最高') else 0,
                    "low_price": float(item.get('最低', 0)) if item.get('最低') else 0,
                    "prev_close": float(item.get('昨收', 0)) if item.get('昨收') else 0,
                    "volume": int(item.get('成交量', 0)) if item.get('成交量') else 0,
                    "amount": float(item.get('成交额', 0)) if item.get('成交额') else 0,
                    "turnover_rate": float(item.get('换手率', 0)) if item.get('换手率') else 0,
                    "pe_ratio": float(item.get('市盈率-动态', 0)) if item.get('市盈率-动态') else None,
                    "update_time": datetime.now().isoformat()
                }
                for item in akshare_data
                if item.get('代码')
            ]
        
        return []
    
    async def batch_get_stock_data(self, symbols: List[str], days: int = 300) -> Dict[str, List[Dict]]:
        """批量获取股票数据"""
        results = {}
        
        # 分批处理，避免过载
        batch_size = 5
        for i in range(0, len(symbols), batch_size):
            batch = symbols[i:i + batch_size]
            
            tasks = [
                self.get_stock_zh_a_hist(symbol, start_date=(datetime.now() - timedelta(days=days)).strftime('%Y%m%d'))
                for symbol in batch
            ]
            
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for symbol, result in zip(batch, batch_results):
                if isinstance(result, Exception):
                    logger.error(f"AKShare获取{symbol}数据失败: {result}")
                    results[symbol] = []
                else:
                    results[symbol] = self.convert_to_standard_format(result, "daily")
            
            # 批次间延迟，避免被限制
            await asyncio.sleep(2)
        
        return results
    
    async def cleanup_old_cache(self, days_to_keep: int = 7):
        """清理旧缓存文件"""
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        
        for cache_file in self.cache_dir.glob("*.json"):
            try:
                file_date = datetime.fromtimestamp(cache_file.stat().st_mtime)
                if file_date < cutoff_date:
                    cache_file.unlink()
                    logger.info(f"删除AKShare旧缓存文件: {cache_file}")
            except Exception as e:
                logger.warning(f"删除AKShare缓存文件失败: {e}")
    
    def __del__(self):
        """清理资源"""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=False)

# 创建全局实例
akshare_service = AKShareDataService()

# 导出主要接口
async def get_stock_list_akshare() -> List[Dict]:
    """获取股票列表（AKShare）"""
    return await akshare_service.get_stock_info_a()

async def get_stock_daily_data_akshare(symbol: str, days: int = 300) -> List[Dict]:
    """获取股票日线数据（AKShare）"""
    start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')
    data = await akshare_service.get_stock_zh_a_hist(symbol, start_date=start_date)
    return akshare_service.convert_to_standard_format(data, "daily")

async def get_realtime_quotes_akshare() -> List[Dict]:
    """获取实时行情（AKShare）"""
    data = await akshare_service.get_stock_zh_a_spot_em()
    return akshare_service.convert_to_standard_format(data, "realtime")
