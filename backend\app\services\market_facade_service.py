"""
统一市场服务门面
整合所有分散的市场服务实现，解决服务层重复问题
实现"服务只留一层"的原则
"""
import asyncio
import logging
from typing import Optional, List, Dict, Any, Union
from datetime import datetime, timedelta
from dataclasses import dataclass

from app.core.unified_cache import unified_cache, CacheType
from app.core.rate_limiter import ApiClient, RateLimitConfig, RetryConfig, CircuitBreakerConfig

logger = logging.getLogger(__name__)


@dataclass
class QuoteData:
    """统一行情数据结构"""
    symbol: str
    name: str
    current_price: float
    change: float
    change_percent: float
    open_price: Optional[float] = None
    high_price: Optional[float] = None
    low_price: Optional[float] = None
    prev_close: Optional[float] = None
    volume: Optional[int] = None
    amount: Optional[float] = None
    turnover_rate: Optional[float] = None
    pe_ratio: Optional[float] = None
    timestamp: Optional[datetime] = None


@dataclass
class KLineData:
    """统一K线数据结构"""
    timestamp: int
    open: float
    high: float
    low: float
    close: float
    volume: int
    amount: Optional[float] = None


class MarketFacadeService:
    """
    统一市场服务门面
    整合所有市场数据相关功能，避免重复实现
    """
    
    def __init__(self):
        self._initialized = False
        self._data_sources = {}
        self._fallback_enabled = True
        
        # 配置限流和重试
        self.api_client = None
    
    async def initialize(self):
        """初始化服务"""
        if self._initialized:
            return
        
        try:
            # 初始化统一缓存
            await unified_cache.initialize()
            
            # 初始化API客户端
            self.api_client = ApiClient(
                name="market_facade",
                rate_limit_config=RateLimitConfig(max_calls=100, time_window=60),
                retry_config=RetryConfig(max_retries=3, base_delay=1.0),
                circuit_breaker_config=CircuitBreakerConfig(failure_threshold=5)
            )
            
            # 初始化数据源
            await self._initialize_data_sources()
            
            self._initialized = True
            logger.info("Market facade service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize market facade service: {e}")
            raise
    
    async def _initialize_data_sources(self):
        """初始化数据源"""
        try:
            # 尝试导入真实数据源
            from app.services.adapters.market_data_adapter import DataSourceManager
            self._data_sources['real'] = DataSourceManager()
        except ImportError:
            logger.warning("Real data source not available")
        
        try:
            # 导入Mock数据源
            from app.services.mock_market_service import mock_market_service
            self._data_sources['mock'] = mock_market_service
        except ImportError:
            logger.warning("Mock data source not available")
    
    async def get_realtime_quote(self, symbol: str) -> Optional[QuoteData]:
        """
        获取实时行情
        统一入口，避免重复实现
        """
        await self.initialize()
        
        # 尝试从缓存获取
        cached_quote = await unified_cache.get(CacheType.QUOTE, symbol)
        if cached_quote:
            return QuoteData(**cached_quote)
        
        # 尝试从真实数据源获取
        quote_data = await self._fetch_quote_from_sources(symbol)
        
        if quote_data:
            # 缓存数据
            await unified_cache.set(CacheType.QUOTE, quote_data.__dict__, symbol)
            return quote_data
        
        return None
    
    async def get_batch_quotes(self, symbols: List[str]) -> List[QuoteData]:
        """
        批量获取行情
        """
        await self.initialize()
        
        quotes = []
        for symbol in symbols:
            quote = await self.get_realtime_quote(symbol)
            if quote:
                quotes.append(quote)
        
        return quotes
    
    async def get_kline_data(
        self,
        symbol: str,
        period: str = "1d",
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 100
    ) -> List[KLineData]:
        """
        获取K线数据
        统一入口，避免重复实现
        """
        await self.initialize()
        
        # 构建缓存键
        cache_key_parts = [symbol, period, str(limit)]
        if start_date:
            cache_key_parts.append(start_date.strftime('%Y%m%d'))
        if end_date:
            cache_key_parts.append(end_date.strftime('%Y%m%d'))
        
        # 尝试从缓存获取
        cached_klines = await unified_cache.get(CacheType.KLINE, *cache_key_parts)
        if cached_klines:
            return [KLineData(**kline) for kline in cached_klines]
        
        # 从数据源获取
        klines = await self._fetch_klines_from_sources(
            symbol, period, start_date, end_date, limit
        )
        
        if klines:
            # 缓存数据
            klines_dict = [kline.__dict__ for kline in klines]
            await unified_cache.set(CacheType.KLINE, klines_dict, *cache_key_parts)
        
        return klines
    
    async def search_stocks(self, keyword: str, limit: int = 20) -> List[Dict[str, Any]]:
        """
        搜索股票
        """
        await self.initialize()
        
        # 尝试从缓存获取
        cache_key_parts = [keyword, str(limit)]
        cached_results = await unified_cache.get(CacheType.SEARCH, *cache_key_parts)
        if cached_results:
            return cached_results
        
        # 从数据源搜索
        results = await self._search_from_sources(keyword, limit)
        
        if results:
            # 缓存结果
            await unified_cache.set(CacheType.SEARCH, results, *cache_key_parts)
        
        return results
    
    async def get_market_depth(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        获取市场深度
        """
        await self.initialize()
        
        # 尝试从缓存获取
        cached_depth = await unified_cache.get(CacheType.REALTIME, f"depth_{symbol}")
        if cached_depth:
            return cached_depth
        
        # 从数据源获取
        depth_data = await self._fetch_depth_from_sources(symbol)
        
        if depth_data:
            # 缓存数据（较短TTL）
            await unified_cache.set(CacheType.REALTIME, depth_data, f"depth_{symbol}")
        
        return depth_data
    
    async def _fetch_quote_from_sources(self, symbol: str) -> Optional[QuoteData]:
        """从数据源获取行情"""
        # 优先尝试真实数据源
        if 'real' in self._data_sources:
            try:
                # 这里应该调用真实数据源
                # real_quote = await self._data_sources['real'].get_quote(symbol)
                # if real_quote:
                #     return QuoteData(**real_quote)
                pass
            except Exception as e:
                logger.warning(f"Real data source failed for {symbol}: {e}")
        
        # 回退到Mock数据源
        if 'mock' in self._data_sources and self._fallback_enabled:
            try:
                mock_quote = await self._data_sources['mock'].get_quote(symbol)
                if mock_quote:
                    return QuoteData(
                        symbol=symbol,
                        name=mock_quote.get('name', f'股票{symbol}'),
                        current_price=mock_quote.get('currentPrice', 0.0),
                        change=mock_quote.get('change', 0.0),
                        change_percent=mock_quote.get('changePercent', 0.0),
                        open_price=mock_quote.get('openPrice'),
                        high_price=mock_quote.get('highPrice'),
                        low_price=mock_quote.get('lowPrice'),
                        prev_close=mock_quote.get('prevClose'),
                        volume=mock_quote.get('volume'),
                        amount=mock_quote.get('amount'),
                        turnover_rate=mock_quote.get('turnoverRate'),
                        pe_ratio=mock_quote.get('pe'),
                        timestamp=datetime.now()
                    )
            except Exception as e:
                logger.error(f"Mock data source failed for {symbol}: {e}")
        
        return None
    
    async def _fetch_klines_from_sources(
        self,
        symbol: str,
        period: str,
        start_date: Optional[datetime],
        end_date: Optional[datetime],
        limit: int
    ) -> List[KLineData]:
        """从数据源获取K线数据"""
        # 优先尝试真实数据源
        if 'real' in self._data_sources:
            try:
                # 这里应该调用真实数据源
                pass
            except Exception as e:
                logger.warning(f"Real data source failed for klines {symbol}: {e}")
        
        # 回退到Mock数据源
        if 'mock' in self._data_sources and self._fallback_enabled:
            try:
                mock_klines = await self._data_sources['mock'].get_kline_data(
                    symbol, period=period, limit=limit
                )
                if mock_klines:
                    return [
                        KLineData(
                            timestamp=kline.get('timestamp', 0),
                            open=kline.get('open', 0.0),
                            high=kline.get('high', 0.0),
                            low=kline.get('low', 0.0),
                            close=kline.get('close', 0.0),
                            volume=kline.get('volume', 0),
                            amount=kline.get('amount')
                        )
                        for kline in mock_klines.get('data', [])
                    ]
            except Exception as e:
                logger.error(f"Mock data source failed for klines {symbol}: {e}")
        
        return []
    
    async def _search_from_sources(self, keyword: str, limit: int) -> List[Dict[str, Any]]:
        """从数据源搜索"""
        # 优先尝试真实数据源
        if 'real' in self._data_sources:
            try:
                # 这里应该调用真实数据源搜索
                pass
            except Exception as e:
                logger.warning(f"Real data source search failed for {keyword}: {e}")
        
        # 回退到Mock数据源
        if 'mock' in self._data_sources and self._fallback_enabled:
            try:
                mock_results = await self._data_sources['mock'].search_stocks(keyword, limit)
                return mock_results.get('data', [])
            except Exception as e:
                logger.error(f"Mock data source search failed for {keyword}: {e}")
        
        return []
    
    async def _fetch_depth_from_sources(self, symbol: str) -> Optional[Dict[str, Any]]:
        """从数据源获取深度数据"""
        # 优先尝试真实数据源
        if 'real' in self._data_sources:
            try:
                # 这里应该调用真实数据源
                pass
            except Exception as e:
                logger.warning(f"Real data source depth failed for {symbol}: {e}")
        
        # 回退到Mock数据源
        if 'mock' in self._data_sources and self._fallback_enabled:
            try:
                mock_depth = await self._data_sources['mock'].get_depth(symbol)
                return mock_depth
            except Exception as e:
                logger.error(f"Mock data source depth failed for {symbol}: {e}")
        
        return None
    
    def get_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        stats = {
            'initialized': self._initialized,
            'data_sources': list(self._data_sources.keys()),
            'fallback_enabled': self._fallback_enabled
        }
        
        if self.api_client:
            stats['api_client'] = self.api_client.get_stats()
        
        return stats


# 全局统一市场服务实例
market_facade = MarketFacadeService()
