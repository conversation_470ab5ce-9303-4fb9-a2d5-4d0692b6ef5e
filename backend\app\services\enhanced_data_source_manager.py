"""
增强的数据源管理器
实现数据源健康检查、重试机制、熔断器和降级策略
"""

import asyncio
import logging
import time
import os
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union, Callable
from enum import Enum
from dataclasses import dataclass, field
import json
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

# 导入各个数据源适配器
from .adapters.tushare_adapter import get_tushare_service, TushareConnectionError, TushareRateLimitError
try:
    from .adapters.akshare_adapter import get_akshare_service
except ImportError:
    def get_akshare_service():
        return None
from .mock_market_service import MockMarketService

logger = logging.getLogger(__name__)


class DataSourceType(Enum):
    """数据源类型"""
    TUSHARE = "tushare"
    AKSHARE = "akshare"  
    MOCK = "mock"


class DataSourceStatus(Enum):
    """数据源状态"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"  # 部分功能受限
    UNHEALTHY = "unhealthy"  # 完全不可用
    CIRCUIT_OPEN = "circuit_open"  # 熔断器开启


@dataclass
class DataSourceHealth:
    """数据源健康状态"""
    source_type: DataSourceType
    status: DataSourceStatus
    last_success_time: Optional[float] = None
    last_failure_time: Optional[float] = None
    consecutive_failures: int = 0
    total_requests: int = 0
    successful_requests: int = 0
    average_response_time: float = 0.0
    circuit_breaker_until: Optional[float] = None
    error_message: Optional[str] = None
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_requests == 0:
            return 1.0
        return self.successful_requests / self.total_requests
    
    @property
    def is_circuit_open(self) -> bool:
        """熔断器是否开启"""
        return (self.circuit_breaker_until is not None and 
                time.time() < self.circuit_breaker_until)


@dataclass 
class RetryConfig:
    """重试配置"""
    max_attempts: int = 3
    base_wait_time: float = 1.0
    max_wait_time: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True


@dataclass
class CircuitBreakerConfig:
    """熔断器配置"""
    failure_threshold: int = 5  # 连续失败次数阈值
    success_threshold: int = 3  # 恢复需要的连续成功次数
    timeout_seconds: int = 60  # 熔断器超时时间


class EnhancedDataSourceManager:
    """增强的数据源管理器"""
    
    def __init__(self):
        self.health_status: Dict[DataSourceType, DataSourceHealth] = {}
        self.retry_config = RetryConfig()
        self.circuit_breaker_config = CircuitBreakerConfig()
        
        # 数据源优先级顺序
        self.source_priority = [
            DataSourceType.TUSHARE,
            DataSourceType.AKSHARE, 
            DataSourceType.MOCK
        ]
        
        # 数据源实例
        self._sources = {}
        
        self._initialize_health_status()
        self._initialize_sources()
    
    def _initialize_health_status(self):
        """初始化健康状态"""
        for source_type in DataSourceType:
            self.health_status[source_type] = DataSourceHealth(
                source_type=source_type,
                status=DataSourceStatus.HEALTHY
            )
    
    def _initialize_sources(self):
        """初始化数据源实例"""
        try:
            self._sources[DataSourceType.TUSHARE] = get_tushare_service()
            logger.info("✅ Tushare数据源初始化成功")
        except Exception as e:
            logger.warning(f"⚠️ Tushare数据源初始化失败: {e}")
            self._update_health_status(DataSourceType.TUSHARE, False, str(e))
        
        try:
            # AkShare数据源（如果可用）
            akshare_service = get_akshare_service()
            if akshare_service:
                self._sources[DataSourceType.AKSHARE] = akshare_service
                logger.info("✅ AkShare数据源初始化成功")
        except Exception as e:
            logger.warning(f"⚠️ AkShare数据源初始化失败: {e}")
            if DataSourceType.AKSHARE in self.health_status:
                self._update_health_status(DataSourceType.AKSHARE, False, str(e))
        
        # Mock数据源（总是可用）
        self._sources[DataSourceType.MOCK] = MockMarketService()
        logger.info("✅ Mock数据源初始化成功")
    
    def _update_health_status(self, source_type: DataSourceType, success: bool, error_message: str = None):
        """更新健康状态"""
        health = self.health_status[source_type]
        health.total_requests += 1
        
        current_time = time.time()
        
        if success:
            health.successful_requests += 1
            health.last_success_time = current_time
            health.consecutive_failures = 0
            health.error_message = None
            
            # 检查是否可以关闭熔断器
            if health.is_circuit_open:
                if health.consecutive_failures == 0:
                    # 单次成功，进入半开状态
                    health.circuit_breaker_until = None
                    health.status = DataSourceStatus.DEGRADED
                    logger.info(f"🔄 {source_type.value} 熔断器进入半开状态")
                elif health.consecutive_failures >= self.circuit_breaker_config.success_threshold:
                    # 达到成功阈值，完全恢复
                    health.circuit_breaker_until = None
                    health.status = DataSourceStatus.HEALTHY
                    logger.info(f"✅ {source_type.value} 熔断器已关闭，服务恢复")
            else:
                health.status = DataSourceStatus.HEALTHY
        else:
            health.last_failure_time = current_time
            health.consecutive_failures += 1
            health.error_message = error_message
            
            # 检查是否需要开启熔断器
            if (health.consecutive_failures >= self.circuit_breaker_config.failure_threshold 
                and not health.is_circuit_open):
                health.circuit_breaker_until = current_time + self.circuit_breaker_config.timeout_seconds
                health.status = DataSourceStatus.CIRCUIT_OPEN
                logger.warning(f"🚨 {source_type.value} 熔断器已开启，将在 {self.circuit_breaker_config.timeout_seconds} 秒后重试")
            elif health.consecutive_failures > 0:
                health.status = DataSourceStatus.DEGRADED
    
    async def _make_request_with_circuit_breaker(self, 
                                               source_type: DataSourceType, 
                                               func: Callable, 
                                               *args, **kwargs) -> Any:
        """带熔断器的请求"""
        health = self.health_status[source_type]
        
        # 检查熔断器状态
        if health.is_circuit_open:
            raise Exception(f"{source_type.value} 服务熔断中，预计恢复时间: {datetime.fromtimestamp(health.circuit_breaker_until)}")
        
        start_time = time.time()
        
        try:
            # 执行请求
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            # 计算响应时间
            response_time = time.time() - start_time
            if health.average_response_time == 0:
                health.average_response_time = response_time
            else:
                health.average_response_time = (health.average_response_time * 0.7 + response_time * 0.3)
            
            self._update_health_status(source_type, True)
            return result
            
        except Exception as e:
            self._update_health_status(source_type, False, str(e))
            raise
    
    async def _execute_with_fallback(self, operation_name: str, func_name: str, *args, **kwargs) -> Any:
        """带降级的执行"""
        last_error = None
        
        for source_type in self.source_priority:
            if source_type not in self._sources:
                continue
                
            source = self._sources[source_type]
            health = self.health_status[source_type]
            
            # 跳过熔断的数据源（除非是最后一个）
            if health.is_circuit_open and source_type != self.source_priority[-1]:
                logger.debug(f"⏭️ 跳过熔断中的 {source_type.value} 数据源")
                continue
            
            try:
                logger.info(f"🔄 尝试使用 {source_type.value} 执行 {operation_name}")
                
                # 获取对应的方法
                if hasattr(source, func_name):
                    func = getattr(source, func_name)
                    result = await self._make_request_with_circuit_breaker(
                        source_type, func, *args, **kwargs
                    )
                    
                    logger.info(f"✅ {source_type.value} 执行 {operation_name} 成功")
                    return result
                else:
                    logger.warning(f"⚠️ {source_type.value} 不支持 {func_name} 方法")
                    continue
                    
            except Exception as e:
                last_error = e
                logger.warning(f"❌ {source_type.value} 执行 {operation_name} 失败: {e}")
                
                # 如果不是最后一个数据源，继续尝试下一个
                if source_type != self.source_priority[-1]:
                    continue
                else:
                    # 最后一个数据源也失败了
                    logger.error(f"🚨 所有数据源执行 {operation_name} 均失败")
                    break
        
        # 所有数据源都失败，抛出最后的错误
        if last_error:
            raise last_error
        else:
            raise Exception(f"没有可用的数据源执行 {operation_name}")
    
    async def get_stock_basic(self, exchange: str = "", list_status: str = "L"):
        """获取股票基础信息"""
        return await self._execute_with_fallback(
            "获取股票基础信息", 
            "get_stock_basic", 
            exchange=exchange, 
            list_status=list_status
        )
    
    async def get_daily_data(self, ts_code: str, start_date: str = None, end_date: str = None, limit: int = None):
        """获取日线数据"""
        return await self._execute_with_fallback(
            "获取日线数据",
            "get_daily_data",
            ts_code=ts_code,
            start_date=start_date,
            end_date=end_date,
            limit=limit
        )
    
    async def get_realtime_quotes(self, symbols: List[str]):
        """获取实时行情"""
        return await self._execute_with_fallback(
            "获取实时行情",
            "get_realtime_quotes", 
            symbols=symbols
        )
    
    async def health_check(self) -> Dict[str, Any]:
        """全面健康检查"""
        results = {
            "timestamp": time.time(),
            "overall_status": "healthy",
            "data_sources": {},
            "active_source": None,
            "circuit_breakers": [],
            "recommendations": []
        }
        
        healthy_sources = 0
        total_sources = len(DataSourceType)
        
        for source_type in DataSourceType:
            if source_type not in self._sources:
                continue
                
            try:
                source = self._sources[source_type]
                if hasattr(source, 'health_check'):
                    health_result = await source.health_check()
                else:
                    health_result = {"status": "unknown", "message": "健康检查方法不可用"}
                
                health = self.health_status[source_type]
                
                results["data_sources"][source_type.value] = {
                    "status": health.status.value,
                    "last_success": health.last_success_time,
                    "last_failure": health.last_failure_time,
                    "consecutive_failures": health.consecutive_failures,
                    "success_rate": health.success_rate,
                    "average_response_time": health.average_response_time,
                    "circuit_breaker_active": health.is_circuit_open,
                    "circuit_breaker_until": health.circuit_breaker_until,
                    "error_message": health.error_message,
                    "details": health_result
                }
                
                if health.status in [DataSourceStatus.HEALTHY, DataSourceStatus.DEGRADED]:
                    healthy_sources += 1
                    
                if health.is_circuit_open:
                    results["circuit_breakers"].append({
                        "source": source_type.value,
                        "until": health.circuit_breaker_until,
                        "reason": health.error_message
                    })
                    
            except Exception as e:
                logger.error(f"健康检查失败 {source_type.value}: {e}")
                results["data_sources"][source_type.value] = {
                    "status": "error",
                    "error": str(e)
                }
        
        # 确定整体状态
        if healthy_sources == 0:
            results["overall_status"] = "critical"
            results["recommendations"].append("所有数据源均不可用，请检查网络连接和配置")
        elif healthy_sources < total_sources / 2:
            results["overall_status"] = "degraded"
            results["recommendations"].append("部分数据源不可用，建议检查失败的数据源")
        
        # 确定当前活跃数据源
        for source_type in self.source_priority:
            health = self.health_status.get(source_type)
            if health and not health.is_circuit_open:
                results["active_source"] = source_type.value
                break
        
        return results
    
    async def perform_minimal_probe(self) -> Dict[str, Any]:
        """执行最小化探测调用"""
        logger.info("🔍 开始执行数据源最小化探测...")
        
        probe_results = {}
        
        for source_type in self.source_priority:
            if source_type not in self._sources:
                continue
                
            source = self._sources[source_type]
            probe_start = time.time()
            
            try:
                logger.info(f"🔍 探测 {source_type.value} 数据源...")
                
                # 执行最小化探测调用
                if source_type == DataSourceType.TUSHARE:
                    # Tushare: 获取交易日历
                    if hasattr(source, '_test_connection'):
                        await asyncio.wait_for(
                            asyncio.get_event_loop().run_in_executor(None, source._test_connection),
                            timeout=10.0
                        )
                        probe_results[source_type.value] = {
                            "status": "success",
                            "response_time": time.time() - probe_start,
                            "method": "trade_cal"
                        }
                elif source_type == DataSourceType.MOCK:
                    # Mock: 总是成功
                    probe_results[source_type.value] = {
                        "status": "success", 
                        "response_time": time.time() - probe_start,
                        "method": "mock_data"
                    }
                
                self._update_health_status(source_type, True)
                logger.info(f"✅ {source_type.value} 探测成功")
                
            except Exception as e:
                probe_results[source_type.value] = {
                    "status": "failed",
                    "error": str(e),
                    "response_time": time.time() - probe_start
                }
                self._update_health_status(source_type, False, str(e))
                logger.warning(f"❌ {source_type.value} 探测失败: {e}")
        
        return probe_results
    
    def get_health_metrics(self) -> Dict[str, Any]:
        """获取健康指标（用于监控端点）"""
        metrics = {
            "timestamp": time.time(),
            "sources": {}
        }
        
        for source_type, health in self.health_status.items():
            metrics["sources"][source_type.value] = {
                "status": health.status.value,
                "success_rate": health.success_rate,
                "consecutive_failures": health.consecutive_failures,
                "average_response_time_ms": health.average_response_time * 1000,
                "last_success_time": health.last_success_time,
                "last_failure_time": health.last_failure_time,
                "circuit_breaker_active": health.is_circuit_open,
                "total_requests": health.total_requests,
                "successful_requests": health.successful_requests
            }
        
        return metrics
    
    async def force_reset_circuit_breaker(self, source_type: DataSourceType):
        """强制重置熔断器"""
        if source_type in self.health_status:
            health = self.health_status[source_type]
            health.circuit_breaker_until = None
            health.consecutive_failures = 0
            health.status = DataSourceStatus.HEALTHY
            logger.info(f"🔄 {source_type.value} 熔断器已强制重置")


# 全局实例
_data_source_manager = None


def get_data_source_manager() -> EnhancedDataSourceManager:
    """获取数据源管理器单例"""
    global _data_source_manager
    if _data_source_manager is None:
        _data_source_manager = EnhancedDataSourceManager()
    return _data_source_manager


async def init_data_source_manager():
    """初始化数据源管理器"""
    manager = get_data_source_manager()
    
    # 执行初始探测
    probe_results = await manager.perform_minimal_probe()
    logger.info(f"📊 数据源探测结果: {probe_results}")
    
    # 获取健康状况
    health = await manager.health_check()
    logger.info(f"📊 数据源健康状况: {health['overall_status']}")
    
    return manager