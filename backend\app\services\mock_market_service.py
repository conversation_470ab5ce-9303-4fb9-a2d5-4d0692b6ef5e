"""
模拟市场数据服务
提供实时行情、历史数据等市场数据
"""
import random
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging

logger = logging.getLogger(__name__)

class MockMarketService:
    """模拟市场数据服务"""
    
    def __init__(self):
        # A股主要股票列表
        self.stock_list = [
            {"code": "000001", "name": "平安银行", "market": "SZ", "sector": "银行"},
            {"code": "000002", "name": "万科A", "market": "SZ", "sector": "房地产"},
            {"code": "000858", "name": "五粮液", "market": "SZ", "sector": "食品饮料"},
            {"code": "000333", "name": "美的集团", "market": "SZ", "sector": "家电"},
            {"code": "002415", "name": "海康威视", "market": "SZ", "sector": "电子"},
            {"code": "300750", "name": "宁德时代", "market": "SZ", "sector": "新能源"},
            {"code": "600036", "name": "招商银行", "market": "SH", "sector": "银行"},
            {"code": "600519", "name": "贵州茅台", "market": "SH", "sector": "食品饮料"},
            {"code": "600276", "name": "恒瑞医药", "market": "SH", "sector": "医药"},
            {"code": "601318", "name": "中国平安", "market": "SH", "sector": "保险"},
            {"code": "600900", "name": "长江电力", "market": "SH", "sector": "公用事业"},
            {"code": "601012", "name": "隆基绿能", "market": "SH", "sector": "新能源"},
        ]
        
        # 基础价格数据
        self.base_prices = {
            "000001": 12.5, "000002": 15.8, "000858": 168.5, "000333": 58.6,
            "002415": 38.9, "300750": 238.5, "600036": 38.2, "600519": 1680.0,
            "600276": 48.6, "601318": 45.8, "600900": 22.5, "601012": 45.8
        }
        
        # 实时价格缓存
        self.current_prices = self.base_prices.copy()
        
        # WebSocket连接管理
        self.websocket_connections = set()
        
    async def get_stock_list(self, market: Optional[str] = None) -> List[Dict]:
        """获取股票列表"""
        if market:
            return [s for s in self.stock_list if s["market"] == market]
        return self.stock_list
    
    async def get_realtime_quote(self, symbol: str) -> Dict:
        """获取实时行情"""
        base_price = self.base_prices.get(symbol, 10.0)
        current_price = self.current_prices.get(symbol, base_price)
        
        # 生成随机波动
        change_percent = random.uniform(-0.02, 0.02)  # ±2%波动
        new_price = current_price * (1 + change_percent)
        
        # 更新缓存
        self.current_prices[symbol] = new_price
        
        # 查找股票信息
        stock_info = next((s for s in self.stock_list if s["code"] == symbol), {})
        
        # 生成行情数据
        quote = {
            "symbol": symbol,
            "code": symbol,
            "name": stock_info.get("name", "未知股票"),
            "market": stock_info.get("market", "SZ"),
            "sector": stock_info.get("sector", "其他"),
            "price": round(new_price, 2),
            "open": round(base_price * random.uniform(0.99, 1.01), 2),
            "high": round(new_price * random.uniform(1.0, 1.02), 2),
            "low": round(new_price * random.uniform(0.98, 1.0), 2),
            "close": round(new_price, 2),
            "pre_close": round(base_price, 2),
            "volume": random.randint(1000000, 50000000),
            "amount": random.randint(10000000, 500000000),
            "change": round(new_price - base_price, 2),
            "change_percent": round((new_price - base_price) / base_price * 100, 2),
            "bid_price": round(new_price - 0.01, 2),
            "ask_price": round(new_price + 0.01, 2),
            "bid_volume": random.randint(100, 10000) * 100,
            "ask_volume": random.randint(100, 10000) * 100,
            "timestamp": datetime.now().isoformat(),
            "status": "TRADING"  # TRADING, SUSPEND, HALT
        }
        
        # 生成五档行情
        quote["bids"] = [
            {
                "price": round(new_price - 0.01 * (i + 1), 2),
                "volume": random.randint(100, 1000) * 100
            }
            for i in range(5)
        ]
        
        quote["asks"] = [
            {
                "price": round(new_price + 0.01 * (i + 1), 2),
                "volume": random.randint(100, 1000) * 100
            }
            for i in range(5)
        ]
        
        return quote
    
    async def get_batch_quotes(self, symbols: List[str]) -> List[Dict]:
        """批量获取实时行情"""
        quotes = []
        for symbol in symbols:
            quote = await self.get_realtime_quote(symbol)
            quotes.append(quote)
        return quotes
    
    async def get_kline_data(
        self,
        symbol: str,
        period: str = "1d",
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict]:
        """获取K线数据"""
        base_price = self.base_prices.get(symbol, 10.0)
        
        # 生成历史K线数据
        klines = []
        current_date = datetime.now()
        
        for i in range(limit):
            # 根据周期计算时间
            if period == "1d":
                date = current_date - timedelta(days=i)
            elif period == "1h":
                date = current_date - timedelta(hours=i)
            elif period == "5m":
                date = current_date - timedelta(minutes=i*5)
            else:
                date = current_date - timedelta(days=i)
            
            # 生成OHLCV数据
            open_price = base_price * random.uniform(0.95, 1.05)
            close_price = base_price * random.uniform(0.95, 1.05)
            high_price = max(open_price, close_price) * random.uniform(1.0, 1.02)
            low_price = min(open_price, close_price) * random.uniform(0.98, 1.0)
            
            kline = {
                "timestamp": date.isoformat(),
                "open": round(open_price, 2),
                "high": round(high_price, 2),
                "low": round(low_price, 2),
                "close": round(close_price, 2),
                "volume": random.randint(1000000, 50000000),
                "amount": random.randint(10000000, 500000000)
            }
            
            klines.append(kline)
        
        # 按时间正序排列
        klines.reverse()
        
        return klines
    
    async def get_tick_data(self, symbol: str, limit: int = 100) -> List[Dict]:
        """获取逐笔成交数据"""
        current_price = self.current_prices.get(symbol, 10.0)
        ticks = []
        
        current_time = datetime.now()
        
        for i in range(limit):
            tick_time = current_time - timedelta(seconds=i)
            
            tick = {
                "timestamp": tick_time.isoformat(),
                "price": round(current_price * random.uniform(0.99, 1.01), 2),
                "volume": random.randint(1, 100) * 100,
                "side": random.choice(["BUY", "SELL"]),
                "trade_id": f"T{tick_time.strftime('%Y%m%d%H%M%S')}{i:04d}"
            }
            
            ticks.append(tick)
        
        return ticks
    
    async def get_market_depth(self, symbol: str) -> Dict:
        """获取市场深度数据"""
        current_price = self.current_prices.get(symbol, 10.0)
        
        depth = {
            "symbol": symbol,
            "timestamp": datetime.now().isoformat(),
            "bids": [],
            "asks": []
        }
        
        # 生成10档买卖盘
        for i in range(10):
            depth["bids"].append({
                "price": round(current_price - 0.01 * (i + 1), 2),
                "volume": random.randint(100, 5000) * 100,
                "order_count": random.randint(1, 50)
            })
            
            depth["asks"].append({
                "price": round(current_price + 0.01 * (i + 1), 2),
                "volume": random.randint(100, 5000) * 100,
                "order_count": random.randint(1, 50)
            })
        
        return depth
    
    async def subscribe_realtime(self, symbols: List[str], websocket):
        """订阅实时行情"""
        self.websocket_connections.add(websocket)
        
        try:
            while True:
                # 每秒推送一次数据
                for symbol in symbols:
                    quote = await self.get_realtime_quote(symbol)
                    await websocket.send_json({
                        "type": "quote",
                        "data": quote
                    })
                
                await asyncio.sleep(1)
                
        except Exception as e:
            self.websocket_connections.discard(websocket)
            raise e
    
    async def get_market_overview(self) -> Dict:
        """获取市场概览 - 使用历史数据模式"""
        try:
            # 获取历史指数数据作为基础
            historical_indices = await self._get_historical_indices_data()

            # 计算涨跌统计（基于历史数据趋势）
            up_count = 0
            down_count = 0
            flat_count = 0

            total_volume = 0
            total_amount = 0

            # 基于历史数据计算市场统计
            for symbol in self.base_prices.keys():
                # 使用历史数据的变化趋势而不是随机数
                historical_change = self._get_historical_change_trend(symbol)
                current = self.base_prices[symbol] * (1 + historical_change / 100)
                base = self.base_prices[symbol]

                if current > base:
                    up_count += 1
                elif current < base:
                    down_count += 1
                else:
                    flat_count += 1

                # 使用历史成交量数据的合理范围
                total_volume += self._get_historical_volume(symbol)
                total_amount += self._get_historical_amount(symbol)

            return {
                "timestamp": datetime.now().isoformat(),
                "market_status": self._get_market_status(),
                "stats": {
                    "advancers": up_count,
                    "decliners": down_count,
                    "unchanged": flat_count,
                    "total": len(self.stock_list),
                    "total_volume": total_volume,
                    "total_amount": total_amount
                },
                "indices": historical_indices
            }

        except Exception as e:
            logger.error(f"获取历史数据失败，使用备用数据: {e}")
            return self._get_fallback_market_overview()

    async def _get_historical_indices_data(self) -> List[Dict]:
        """获取基于历史数据的指数信息"""
        # 这里可以集成真实的历史数据源
        # 目前使用基于历史趋势的合理数据

        indices_data = [
            {
                "code": "000001.SH",
                "name": "上证指数",
                "current": self._get_historical_index_value("000001.SH", 3245.68),
                "change": self._get_historical_index_change("000001.SH"),
                "change_percent": self._get_historical_index_change_percent("000001.SH"),
                "volume": 245680000,
                "turnover": 345678900000
            },
            {
                "code": "399001.SZ",
                "name": "深证成指",
                "current": self._get_historical_index_value("399001.SZ", 10856.34),
                "change": self._get_historical_index_change("399001.SZ"),
                "change_percent": self._get_historical_index_change_percent("399001.SZ"),
                "volume": 189450000,
                "turnover": 278945600000
            },
            {
                "code": "399006.SZ",
                "name": "创业板指",
                "current": self._get_historical_index_value("399006.SZ", 2234.56),
                "change": self._get_historical_index_change("399006.SZ"),
                "change_percent": self._get_historical_index_change_percent("399006.SZ"),
                "volume": 156780000,
                "turnover": 198765400000
            },
            {
                "code": "000688.SH",
                "name": "科创50",
                "current": self._get_historical_index_value("000688.SH", 1045.23),
                "change": self._get_historical_index_change("000688.SH"),
                "change_percent": self._get_historical_index_change_percent("000688.SH"),
                "volume": 98765000,
                "turnover": 123456700000
            }
        ]

        return indices_data

    def _get_historical_change_trend(self, symbol: str) -> float:
        """基于历史数据获取变化趋势"""
        # 使用符号的哈希值作为种子，确保同一股票的变化趋势相对稳定
        random.seed(hash(symbol) + int(datetime.now().strftime("%Y%m%d")))
        # 模拟历史数据的正态分布变化，大部分在-3%到3%之间
        change = random.normalvariate(0, 1.5)
        return max(-9.9, min(9.9, change))  # 限制在合理范围内

    def _get_historical_volume(self, symbol: str) -> int:
        """基于历史数据获取成交量"""
        # 根据股票代码生成相对稳定的成交量基数
        base_volume = 10000000 + (hash(symbol) % 50000000)
        # 添加日内波动
        daily_factor = 0.8 + random.random() * 0.4  # 0.8-1.2倍波动
        return int(base_volume * daily_factor)

    def _get_historical_amount(self, symbol: str) -> int:
        """基于历史数据获取成交额"""
        volume = self._get_historical_volume(symbol)
        # 假设平均价格在10-100元之间
        avg_price = 10 + (hash(symbol) % 90)
        return int(volume * avg_price)

    def _get_market_status(self) -> str:
        """获取市场状态"""
        now = datetime.now()
        hour = now.hour
        minute = now.minute

        # 简单的交易时间判断
        if (9 <= hour < 11) or (13 <= hour < 15):
            return "TRADING"
        elif hour == 11 and minute < 30:
            return "BREAK"
        else:
            return "CLOSED"

    def _get_historical_index_value(self, index_code: str, base_value: float) -> float:
        """获取基于历史数据的指数值"""
        # 使用日期和指数代码作为种子
        random.seed(hash(index_code) + int(datetime.now().strftime("%Y%m%d")))
        # 指数变化相对较小，通常在-2%到2%之间
        change_percent = random.normalvariate(0, 0.8)
        change_percent = max(-3.0, min(3.0, change_percent))
        return round(base_value * (1 + change_percent / 100), 2)

    def _get_historical_index_change(self, index_code: str) -> float:
        """获取指数变化值"""
        random.seed(hash(index_code) + int(datetime.now().strftime("%Y%m%d")))
        if "000001" in index_code:  # 上证指数
            return round(random.normalvariate(0, 15), 2)
        elif "399001" in index_code:  # 深证成指
            return round(random.normalvariate(0, 50), 2)
        elif "399006" in index_code:  # 创业板指
            return round(random.normalvariate(0, 20), 2)
        else:  # 科创50
            return round(random.normalvariate(0, 10), 2)

    def _get_historical_index_change_percent(self, index_code: str) -> float:
        """获取指数变化百分比"""
        random.seed(hash(index_code) + int(datetime.now().strftime("%Y%m%d")))
        change_percent = random.normalvariate(0, 0.8)
        return round(max(-3.0, min(3.0, change_percent)), 2)

    def _get_fallback_market_overview(self) -> Dict:
        """备用市场概览数据"""
        return {
            "timestamp": datetime.now().isoformat(),
            "market_status": "TRADING",
            "stats": {
                "advancers": 1200,
                "decliners": 800,
                "unchanged": 100,
                "total": 2100,
                "total_volume": 15000000000,
                "total_amount": 180000000000
            },
            "indices": [
                {
                    "code": "000001.SH",
                    "name": "上证指数",
                    "current": 3245.68,
                    "change": 12.45,
                    "change_percent": 0.38,
                    "volume": 245680000,
                    "turnover": 345678900000
                }
            ]
        }

    async def search_stocks(self, keyword: str) -> List[Dict]:
        """搜索股票"""
        keyword = keyword.lower()
        results = []
        
        for stock in self.stock_list:
            if (keyword in stock["code"].lower() or 
                keyword in stock["name"].lower() or
                keyword in stock.get("pinyin", "").lower()):
                results.append(stock)
        
        return results
    
    async def get_sector_performance(self) -> List[Dict]:
        """获取板块表现"""
        sectors = ["银行", "房地产", "食品饮料", "家电", "电子", "新能源", "医药", "保险", "公用事业"]
        
        performance = []
        for sector in sectors:
            perf = {
                "sector": sector,
                "change_percent": round(random.uniform(-3, 3), 2),
                "volume": random.randint(10000000, 500000000),
                "amount": random.randint(100000000, 5000000000),
                "up_count": random.randint(5, 50),
                "down_count": random.randint(5, 50),
                "leader": random.choice([s for s in self.stock_list if s.get("sector") == sector] or [{"name": "龙头股"}]).get("name", "龙头股")
            }
            performance.append(perf)
        
        # 按涨跌幅排序
        performance.sort(key=lambda x: x["change_percent"], reverse=True)
        
        return performance


# 创建全局实例
mock_market_service = MockMarketService()