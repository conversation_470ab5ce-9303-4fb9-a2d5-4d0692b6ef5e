"""
增强历史数据服务
基于数据库存储，提供高性能的历史数据查询和管理
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import asyncio
from sqlalchemy import select, func, and_, or_, desc, asc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.core.database import get_db
from app.core.unified_cache import unified_cache, CacheType
from app.db.models.historical_data import (
    HistoricalKLineData, 
    StockBasicInfo, 
    DataImportLog,
    MarketCalendar
)

logger = logging.getLogger(__name__)


class EnhancedHistoricalService:
    """增强历史数据服务"""
    
    def __init__(self):
        self.batch_size = 1000  # 批量处理大小
        self.cache_ttl = 3600   # 缓存1小时
        
    async def get_stock_list(
        self,
        db: AsyncSession,
        market: Optional[str] = None,
        industry: Optional[str] = None,
        status: str = "L",
        page: int = 1,
        page_size: int = 50,
        order_by: str = "symbol",
        order_direction: str = "asc"
    ) -> Dict[str, Any]:
        """
        获取股票列表 - 基于数据库查询
        """
        try:
            # 构建查询条件
            conditions = [StockBasicInfo.status == status]
            
            if market:
                conditions.append(StockBasicInfo.market == market.upper())
            if industry:
                conditions.append(StockBasicInfo.industry == industry)
            
            # 构建基础查询
            query = select(StockBasicInfo).where(and_(*conditions))
            
            # 排序
            if order_direction.lower() == "desc":
                query = query.order_by(desc(getattr(StockBasicInfo, order_by, StockBasicInfo.symbol)))
            else:
                query = query.order_by(asc(getattr(StockBasicInfo, order_by, StockBasicInfo.symbol)))
            
            # 获取总数
            count_query = select(func.count()).select_from(
                select(StockBasicInfo).where(and_(*conditions)).subquery()
            )
            total_result = await db.execute(count_query)
            total = total_result.scalar()
            
            # 分页
            offset = (page - 1) * page_size
            query = query.offset(offset).limit(page_size)
            
            result = await db.execute(query)
            stocks = result.scalars().all()
            
            # 转换为字典格式
            stock_list = []
            for stock in stocks:
                stock_list.append({
                    'symbol': stock.symbol,
                    'name': stock.name,
                    'market': stock.market,
                    'industry': stock.industry or '其他',
                    'sector': stock.sector,
                    'total_records': stock.total_records,
                    'first_trade_date': stock.first_trade_date.strftime('%Y-%m-%d') if stock.first_trade_date else None,
                    'last_trade_date': stock.last_trade_date.strftime('%Y-%m-%d') if stock.last_trade_date else None,
                    'status': stock.status,
                    'market_cap': stock.market_cap
                })
            
            return {
                'stocks': stock_list,
                'total': total,
                'page': page,
                'page_size': page_size,
                'total_pages': (total + page_size - 1) // page_size
            }
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return {
                'stocks': [], 
                'total': 0, 
                'page': page, 
                'page_size': page_size,
                'total_pages': 0
            }
    
    async def get_stock_data(
        self,
        db: AsyncSession,
        symbol: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        fields: Optional[List[str]] = None,
        limit: Optional[int] = None,
        use_cache: bool = True
    ) -> Optional[List[Dict[str, Any]]]:
        """
        获取股票历史数据 - 基于数据库查询
        """
        try:
            # 构建缓存键
            cache_key_parts = [
                symbol,
                start_date.strftime('%Y%m%d') if start_date else 'all',
                end_date.strftime('%Y%m%d') if end_date else 'all',
                '_'.join(fields) if fields else 'all',
                str(limit) if limit else 'all'
            ]
            
            # 检查缓存
            if use_cache:
                cached_data = await unified_cache.get(CacheType.HISTORICAL, *cache_key_parts)
                if cached_data is not None:
                    logger.debug(f"从缓存获取数据: {symbol}")
                    return cached_data
            
            # 构建查询条件
            conditions = [HistoricalKLineData.symbol == symbol]
            
            if start_date:
                conditions.append(HistoricalKLineData.trade_date >= start_date)
            if end_date:
                conditions.append(HistoricalKLineData.trade_date <= end_date)
            
            # 构建查询
            if fields:
                # 选择特定字段
                selected_fields = []
                for field in fields:
                    if hasattr(HistoricalKLineData, field):
                        selected_fields.append(getattr(HistoricalKLineData, field))
                
                if not selected_fields:
                    selected_fields = [HistoricalKLineData]
                
                query = select(*selected_fields)
            else:
                query = select(HistoricalKLineData)
            
            query = query.where(and_(*conditions)).order_by(desc(HistoricalKLineData.trade_date))
            
            if limit:
                query = query.limit(limit)
            
            result = await db.execute(query)
            rows = result.all()
            
            # 转换为字典格式
            data_list = []
            for row in rows:
                if hasattr(row, '__dict__'):
                    # 完整对象
                    row_dict = {
                        '日期': row.trade_date.strftime('%Y-%m-%d'),
                        '开盘价': float(row.open_price),
                        '收盘价': float(row.close_price),
                        '最高价': float(row.high_price),
                        '最低价': float(row.low_price),
                        '成交量(手)': int(row.volume),
                        '成交额(元)': float(row.amount),
                        '涨跌额': float(row.change_amount) if row.change_amount else 0,
                        '涨跌幅(%)': float(row.change_percent) if row.change_percent else 0,
                        '振幅(%)': float(row.amplitude) if row.amplitude else 0,
                        '换手率(%)': float(row.turnover_rate) if row.turnover_rate else 0
                    }
                else:
                    # 部分字段
                    row_dict = {}
                    if isinstance(row, tuple):
                        for i, field in enumerate(fields or []):
                            row_dict[field] = row[i]
                    else:
                        row_dict = dict(row._mapping) if hasattr(row, '_mapping') else {}
                
                data_list.append(row_dict)
            
            # 缓存结果
            if use_cache and data_list:
                await unified_cache.set(CacheType.HISTORICAL, data_list, *cache_key_parts)
            
            return data_list
            
        except Exception as e:
            logger.error(f"获取股票 {symbol} 数据失败: {e}")
            return None
    
    async def search_stocks(
        self,
        db: AsyncSession,
        keyword: str,
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """
        搜索股票 - 基于数据库查询
        """
        try:
            keyword_lower = keyword.lower()
            
            # 构建查询条件 - 支持代码和名称搜索
            query = select(StockBasicInfo).where(
                or_(
                    StockBasicInfo.symbol.ilike(f'%{keyword}%'),
                    StockBasicInfo.name.ilike(f'%{keyword}%')
                )
            ).order_by(StockBasicInfo.symbol).limit(limit)
            
            result = await db.execute(query)
            stocks = result.scalars().all()
            
            search_results = []
            for stock in stocks:
                search_results.append({
                    'symbol': stock.symbol,
                    'name': stock.name,
                    'market': stock.market,
                    'industry': stock.industry or '其他',
                    'total_records': stock.total_records
                })
            
            return search_results
            
        except Exception as e:
            logger.error(f"搜索股票失败: {e}")
            return []
    
    async def get_market_statistics(
        self,
        db: AsyncSession
    ) -> Dict[str, Any]:
        """
        获取市场统计信息 - 基于数据库查询
        """
        try:
            # 获取股票总数
            total_query = select(func.count(StockBasicInfo.symbol))
            total_result = await db.execute(total_query)
            total_stocks = total_result.scalar()
            
            # 获取市场分布
            market_query = select(
                StockBasicInfo.market,
                func.count(StockBasicInfo.symbol).label('count')
            ).group_by(StockBasicInfo.market)
            market_result = await db.execute(market_query)
            markets = {row.market: row.count for row in market_result.all()}
            
            # 获取行业分布
            industry_query = select(
                StockBasicInfo.industry,
                func.count(StockBasicInfo.symbol).label('count')
            ).where(StockBasicInfo.industry.isnot(None)).group_by(StockBasicInfo.industry)
            industry_result = await db.execute(industry_query)
            industries = {row.industry: row.count for row in industry_result.all()}
            
            # 获取数据覆盖范围
            coverage_query = select(
                func.min(StockBasicInfo.first_trade_date).label('start_date'),
                func.max(StockBasicInfo.last_trade_date).label('end_date'),
                func.sum(StockBasicInfo.total_records).label('total_records')
            )
            coverage_result = await db.execute(coverage_query)
            coverage = coverage_result.first()
            
            return {
                'total_stocks': total_stocks,
                'markets': markets,
                'industries': industries,
                'data_range': {
                    'start_date': coverage.start_date.strftime('%Y-%m-%d') if coverage.start_date else None,
                    'end_date': coverage.end_date.strftime('%Y-%m-%d') if coverage.end_date else None
                },
                'total_records': coverage.total_records or 0,
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取市场统计失败: {e}")
            return {}
    
    async def import_csv_data(
        self,
        db: AsyncSession,
        csv_file_path: str,
        symbol: str,
        name: str = None,
        update_mode: str = "replace"  # replace/append/update
    ) -> Dict[str, Any]:
        """
        从CSV文件导入历史数据到数据库
        """
        try:
            # 创建导入日志
            import_log = DataImportLog(
                symbol=symbol,
                import_type="csv",
                file_path=csv_file_path,
                status="processing"
            )
            db.add(import_log)
            await db.commit()
            await db.refresh(import_log)
            
            # 读取CSV文件
            df = pd.read_csv(csv_file_path, encoding='utf-8')
            
            if df.empty:
                import_log.status = "failed"
                import_log.error_message = "CSV文件为空"
                await db.commit()
                return {'success': False, 'message': 'CSV文件为空'}
            
            # 数据清洗和标准化
            df = self._clean_csv_data(df)
            
            import_log.total_records = len(df)
            import_log.start_date = df['trade_date'].min()
            import_log.end_date = df['trade_date'].max()
            
            # 删除旧数据（如果是替换模式）
            if update_mode == "replace":
                delete_query = select(HistoricalKLineData).where(
                    HistoricalKLineData.symbol == symbol
                )
                result = await db.execute(delete_query)
                existing_records = result.scalars().all()
                for record in existing_records:
                    await db.delete(record)
            
            # 批量插入新数据
            success_count = 0
            error_count = 0
            
            for i in range(0, len(df), self.batch_size):
                batch_df = df.iloc[i:i + self.batch_size]
                
                try:
                    # 准备批量插入数据
                    records = []
                    for _, row in batch_df.iterrows():
                        record = HistoricalKLineData(
                            symbol=symbol,
                            trade_date=row['trade_date'],
                            open_price=row['open_price'],
                            close_price=row['close_price'],
                            high_price=row['high_price'],
                            low_price=row['low_price'],
                            volume=row['volume'],
                            amount=row['amount'],
                            change_amount=row.get('change_amount'),
                            change_percent=row.get('change_percent'),
                            amplitude=row.get('amplitude'),
                            turnover_rate=row.get('turnover_rate'),
                            data_source="csv_import"
                        )
                        records.append(record)
                    
                    # 批量添加
                    db.add_all(records)
                    await db.commit()
                    
                    success_count += len(records)
                    logger.info(f"成功导入 {symbol} 数据 {i + len(batch_df)}/{len(df)}")
                    
                except Exception as batch_error:
                    logger.error(f"批量导入失败: {batch_error}")
                    await db.rollback()
                    error_count += len(batch_df)
            
            # 更新或创建股票基本信息
            await self._update_stock_basic_info(db, symbol, name, df)
            
            # 更新导入日志
            import_log.success_records = success_count
            import_log.error_records = error_count
            import_log.status = "completed" if error_count == 0 else "partial"
            import_log.import_completed_at = datetime.now()
            
            await db.commit()
            
            # 清除相关缓存
            await unified_cache.clear(CacheType.HISTORICAL)
            
            return {
                'success': True,
                'message': f'成功导入 {success_count} 条记录',
                'total_records': len(df),
                'success_records': success_count,
                'error_records': error_count,
                'import_log_id': import_log.id
            }
            
        except Exception as e:
            logger.error(f"导入CSV数据失败: {e}")
            if 'import_log' in locals():
                import_log.status = "failed"
                import_log.error_message = str(e)
                import_log.import_completed_at = datetime.now()
                await db.commit()
            
            return {'success': False, 'message': f'导入失败: {str(e)}'}
    
    def _clean_csv_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清洗CSV数据
        """
        # 标准化列名映射
        column_mapping = {
            '日期': 'trade_date',
            '开盘价': 'open_price', 
            '开盘': 'open_price',
            '收盘价': 'close_price',
            '收盘': 'close_price',
            '最高价': 'high_price',
            '最高': 'high_price',
            '最低价': 'low_price',
            '最低': 'low_price',
            '成交量': 'volume',
            '成交量(手)': 'volume',
            '成交额': 'amount',
            '成交额(元)': 'amount',
            '涨跌额': 'change_amount',
            '涨跌幅': 'change_percent',
            '涨跌幅(%)': 'change_percent',
            '振幅': 'amplitude',
            '振幅(%)': 'amplitude',
            '换手率': 'turnover_rate',
            '换手率(%)': 'turnover_rate'
        }
        
        # 重命名列
        df = df.rename(columns=column_mapping)
        
        # 确保必需列存在
        required_columns = ['trade_date', 'open_price', 'close_price', 'high_price', 'low_price', 'volume', 'amount']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            raise ValueError(f"CSV文件缺少必需的列: {missing_columns}")
        
        # 数据类型转换和清洗
        df['trade_date'] = pd.to_datetime(df['trade_date'])
        
        # 数值列转换
        numeric_columns = ['open_price', 'close_price', 'high_price', 'low_price', 'volume', 'amount']
        for col in numeric_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 可选列处理
        optional_columns = ['change_amount', 'change_percent', 'amplitude', 'turnover_rate']
        for col in optional_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 删除无效数据
        df = df.dropna(subset=required_columns)
        
        # 价格合理性检查
        df = df[
            (df['open_price'] > 0) &
            (df['close_price'] > 0) &
            (df['high_price'] > 0) &
            (df['low_price'] > 0) &
            (df['high_price'] >= df['low_price']) &
            (df['volume'] >= 0) &
            (df['amount'] >= 0)
        ]
        
        return df
    
    async def _update_stock_basic_info(
        self,
        db: AsyncSession,
        symbol: str,
        name: str,
        df: pd.DataFrame
    ):
        """
        更新股票基本信息
        """
        try:
            # 查找现有记录
            query = select(StockBasicInfo).where(StockBasicInfo.symbol == symbol)
            result = await db.execute(query)
            stock_info = result.scalar_one_or_none()
            
            # 确定市场
            market = self._determine_market(symbol)
            
            if stock_info:
                # 更新现有记录
                stock_info.name = name or stock_info.name
                stock_info.first_trade_date = df['trade_date'].min()
                stock_info.last_trade_date = df['trade_date'].max()
                stock_info.total_records = len(df)
                stock_info.updated_at = datetime.now()
            else:
                # 创建新记录
                stock_info = StockBasicInfo(
                    symbol=symbol,
                    name=name or f"股票{symbol}",
                    market=market,
                    industry=self._determine_industry(name or ""),
                    first_trade_date=df['trade_date'].min(),
                    last_trade_date=df['trade_date'].max(),
                    total_records=len(df),
                    status="L"
                )
                db.add(stock_info)
            
            await db.commit()
            
        except Exception as e:
            logger.error(f"更新股票基本信息失败: {e}")
    
    def _determine_market(self, symbol: str) -> str:
        """根据股票代码判断市场"""
        if symbol.startswith(('00', '30')):
            return 'SZ'  # 深圳
        elif symbol.startswith(('60', '68')):
            return 'SH'  # 上海
        elif symbol.startswith(('8', '4')):
            return 'BJ'  # 北京
        else:
            return 'SH'  # 默认上海
    
    def _determine_industry(self, name: str) -> str:
        """根据股票名称判断行业"""
        industry_keywords = {
            '银行': ['银行', '农商', '城商'],
            '保险': ['保险', '人寿', '财险'],
            '证券': ['证券', '信托', '期货'],
            '房地产': ['地产', '房地产', '置业', '发展'],
            '医药生物': ['医药', '生物', '制药', '医疗'],
            '计算机': ['科技', '软件', '信息', '数据', '网络'],
            '机械设备': ['制造', '机械', '设备', '工业'],
            '公用事业': ['能源', '石油', '煤炭', '电力'],
            '食品饮料': ['消费', '零售', '商贸', '食品', '饮料'],
            '交通运输': ['交通', '运输', '物流', '航空']
        }
        
        for industry, keywords in industry_keywords.items():
            if any(keyword in name for keyword in keywords):
                return industry
        
        return '其他'


# 创建全局实例
enhanced_historical_service = EnhancedHistoricalService()