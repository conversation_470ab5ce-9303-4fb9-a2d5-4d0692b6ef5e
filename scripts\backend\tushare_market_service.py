#!/usr/bin/env python3
"""
Tushare市场数据服务
提供真实的市场数据API，替换模拟数据
"""

import os
import sys
import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import pandas as pd
import tushare as ts
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import os
import urllib.request
import time
import random

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Tushare配置
TUSHARE_TOKEN = "f7eedf159b0e6e4e4fc97a59f11fcb6936201c698a6974cbb8e18400"

class TushareMarketService:
    """Tushare市场数据服务"""
    
    def __init__(self):
        self.pro = None
        self._initialize()
    
    def _initialize(self):
        """初始化Tushare API"""
        try:
            # 禁用代理设置，避免连接问题
            self._disable_proxy()

            ts.set_token(TUSHARE_TOKEN)
            self.pro = ts.pro_api()
            logger.info("✅ Tushare API初始化成功")
        except Exception as e:
            logger.error(f"❌ Tushare API初始化失败: {e}")
            raise

    def _disable_proxy(self):
        """禁用代理设置"""
        try:
            # 清除urllib的默认代理处理器
            proxy_handler = urllib.request.ProxyHandler({})
            opener = urllib.request.build_opener(proxy_handler)
            urllib.request.install_opener(opener)

            # 清除环境变量中的代理设置
            proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY']
            for var in proxy_vars:
                if var in os.environ:
                    del os.environ[var]

            logger.info("✅ 已禁用代理设置")
        except Exception as e:
            logger.warning(f"⚠️ 禁用代理设置失败: {e}")
    
    async def get_market_overview(self) -> Dict[str, Any]:
        """获取市场概览"""
        try:
            # 获取主要指数数据
            indices = ['000001.SH', '399001.SZ', '399006.SZ', '000300.SH', '000905.SH']
            overview_data = []
            
            for index_code in indices:
                try:
                    # 获取指数基本信息
                    df = self.pro.index_basic(ts_code=index_code)
                    if df.empty:
                        continue
                    
                    index_name = df.iloc[0]['name']
                    
                    # 获取最新日线数据
                    daily_df = self.pro.index_daily(
                        ts_code=index_code,
                        start_date=(datetime.now() - timedelta(days=10)).strftime('%Y%m%d'),
                        end_date=datetime.now().strftime('%Y%m%d')
                    )
                    
                    if not daily_df.empty:
                        latest = daily_df.iloc[0]
                        prev = daily_df.iloc[1] if len(daily_df) > 1 else latest
                        
                        change = latest['close'] - prev['close']
                        change_pct = (change / prev['close']) * 100
                        
                        overview_data.append({
                            'code': index_code,
                            'name': index_name,
                            'current': float(latest['close']),
                            'change': float(change),
                            'change_percent': float(change_pct),
                            'volume': int(latest['vol']) if 'vol' in latest else 0,
                            'turnover': float(latest['amount']) if 'amount' in latest else 0.0
                        })
                        
                except Exception as e:
                    logger.warning(f"获取指数 {index_code} 数据失败: {e}")
                    continue
            
            return {
                'indices': overview_data,
                'timestamp': datetime.now().isoformat(),
                'market_status': 'open' if self._is_trading_time() else 'closed'
            }
            
        except Exception as e:
            logger.error(f"获取市场概览失败: {e}")
            raise HTTPException(status_code=500, detail=f"获取市场概览失败: {str(e)}")
    
    async def get_stock_list(self, page_size: int = 100, page: int = 1) -> Dict[str, Any]:
        """获取股票列表"""
        try:
            # 获取股票基本信息
            df = self.pro.stock_basic(
                exchange='',
                list_status='L',
                fields='ts_code,symbol,name,area,industry,market,list_date'
            )
            
            if df.empty:
                return {'stocks': [], 'total': 0, 'page': page, 'page_size': page_size}
            
            # 分页处理
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            page_df = df.iloc[start_idx:end_idx]
            
            stocks = []
            for _, row in page_df.iterrows():
                try:
                    # 获取最新价格数据
                    daily_df = self.pro.daily(
                        ts_code=row['ts_code'],
                        start_date=(datetime.now() - timedelta(days=5)).strftime('%Y%m%d'),
                        end_date=datetime.now().strftime('%Y%m%d'),
                        fields='ts_code,trade_date,close,change,pct_chg,vol,amount'
                    )
                    
                    if not daily_df.empty:
                        latest = daily_df.iloc[0]
                        
                        stocks.append({
                            'symbol': row['symbol'],
                            'name': row['name'],
                            'current_price': float(latest['close']),
                            'change': float(latest['change']) if latest['change'] else 0.0,
                            'change_percent': float(latest['pct_chg']) if latest['pct_chg'] else 0.0,
                            'volume': int(latest['vol']) if latest['vol'] else 0,
                            'turnover': float(latest['amount']) if latest['amount'] else 0.0,
                            'market': row['market'],
                            'industry': row['industry'] if row['industry'] else '--',
                            'ts_code': row['ts_code']
                        })
                    else:
                        # 如果没有价格数据，使用基本信息
                        stocks.append({
                            'symbol': row['symbol'],
                            'name': row['name'],
                            'current_price': 0.0,
                            'change': 0.0,
                            'change_percent': 0.0,
                            'volume': 0,
                            'turnover': 0.0,
                            'market': row['market'],
                            'industry': row['industry'] if row['industry'] else '--',
                            'ts_code': row['ts_code']
                        })
                        
                except Exception as e:
                    logger.warning(f"获取股票 {row['ts_code']} 价格数据失败: {e}")
                    # 使用基本信息
                    stocks.append({
                        'symbol': row['symbol'],
                        'name': row['name'],
                        'current_price': 0.0,
                        'change': 0.0,
                        'change_percent': 0.0,
                        'volume': 0,
                        'turnover': 0.0,
                        'market': row['market'],
                        'industry': row['industry'] if row['industry'] else '--',
                        'ts_code': row['ts_code']
                    })
            
            return {
                'stocks': stocks,
                'total': len(df),
                'page': page,
                'page_size': page_size,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            raise HTTPException(status_code=500, detail=f"获取股票列表失败: {str(e)}")
    
    async def get_sectors(self) -> Dict[str, Any]:
        """获取板块数据"""
        try:
            # 获取行业分类
            df = self.pro.stock_basic(
                exchange='',
                list_status='L',
                fields='industry'
            )
            
            if df.empty:
                return {'sectors': []}
            
            # 统计行业分布
            industry_counts = df['industry'].value_counts()
            
            sectors = []
            for industry, count in industry_counts.head(20).items():
                if pd.notna(industry) and industry != '':
                    sectors.append({
                        'name': industry,
                        'stock_count': int(count),
                        'change_percent': 0.0,  # 需要更复杂的计算
                        'market_cap': 0.0  # 需要更复杂的计算
                    })
            
            return {
                'sectors': sectors,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取板块数据失败: {e}")
            raise HTTPException(status_code=500, detail=f"获取板块数据失败: {str(e)}")
    
    async def get_rankings(self, rank_type: str = 'change_percent', limit: int = 50) -> Dict[str, Any]:
        """获取排行榜数据"""
        try:
            # 获取今日涨跌幅排行
            today = datetime.now().strftime('%Y%m%d')
            
            # 获取所有股票的今日数据
            df = self.pro.daily(
                trade_date=today,
                fields='ts_code,close,change,pct_chg,vol,amount'
            )
            
            if df.empty:
                # 如果今日无数据，获取最近交易日数据
                trade_cal = self.pro.trade_cal(
                    exchange='SSE',
                    start_date=(datetime.now() - timedelta(days=10)).strftime('%Y%m%d'),
                    end_date=today
                )
                
                if not trade_cal.empty:
                    recent_trade_dates = trade_cal[trade_cal['is_open'] == 1]['cal_date'].tolist()
                    if recent_trade_dates:
                        latest_trade_date = recent_trade_dates[-1]
                        df = self.pro.daily(
                            trade_date=latest_trade_date,
                            fields='ts_code,close,change,pct_chg,vol,amount'
                        )
            
            if df.empty:
                return {'rankings': [], 'type': rank_type, 'limit': limit}
            
            # 获取股票基本信息
            stock_basic = self.pro.stock_basic(
                exchange='',
                list_status='L',
                fields='ts_code,symbol,name'
            )
            
            # 合并数据
            merged_df = df.merge(stock_basic, on='ts_code', how='left')
            
            # 根据排行类型排序
            if rank_type == 'change_percent':
                sorted_df = merged_df.sort_values('pct_chg', ascending=False)
            elif rank_type == 'turnover':
                sorted_df = merged_df.sort_values('amount', ascending=False)
            else:
                sorted_df = merged_df.sort_values('pct_chg', ascending=False)
            
            # 取前N名
            top_df = sorted_df.head(limit)
            
            rankings = []
            for idx, (_, row) in enumerate(top_df.iterrows()):
                if pd.notna(row['name']):
                    rankings.append({
                        'rank': idx + 1,
                        'symbol': row['symbol'] if pd.notna(row['symbol']) else row['ts_code'],
                        'name': row['name'],
                        'current_price': float(row['close']) if pd.notna(row['close']) else 0.0,
                        'change': float(row['change']) if pd.notna(row['change']) else 0.0,
                        'change_percent': float(row['pct_chg']) if pd.notna(row['pct_chg']) else 0.0,
                        'volume': int(row['vol']) if pd.notna(row['vol']) else 0,
                        'turnover': float(row['amount']) if pd.notna(row['amount']) else 0.0,
                        'ts_code': row['ts_code']
                    })
            
            return {
                'rankings': rankings,
                'type': rank_type,
                'limit': limit,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取排行榜失败: {e}")
            raise HTTPException(status_code=500, detail=f"获取排行榜失败: {str(e)}")
    
    def _is_trading_time(self) -> bool:
        """判断是否在交易时间"""
        now = datetime.now()
        weekday = now.weekday()
        
        # 周末不交易
        if weekday >= 5:
            return False
        
        # 交易时间：9:30-11:30, 13:00-15:00
        current_time = now.time()
        morning_start = datetime.strptime('09:30', '%H:%M').time()
        morning_end = datetime.strptime('11:30', '%H:%M').time()
        afternoon_start = datetime.strptime('13:00', '%H:%M').time()
        afternoon_end = datetime.strptime('15:00', '%H:%M').time()
        
        return (morning_start <= current_time <= morning_end) or \
               (afternoon_start <= current_time <= afternoon_end)

# 创建全局服务实例
tushare_service = TushareMarketService()

# 测试函数
async def test_all_apis():
    """测试所有API"""
    print("🧪 测试Tushare市场数据服务")
    print("=" * 50)
    
    try:
        # 测试市场概览
        print("📊 测试市场概览...")
        overview = await tushare_service.get_market_overview()
        print(f"   ✅ 获取到 {len(overview['indices'])} 个指数")
        
        # 测试股票列表
        print("📈 测试股票列表...")
        stocks = await tushare_service.get_stock_list(page_size=10)
        print(f"   ✅ 获取到 {len(stocks['stocks'])} 只股票")
        
        # 测试板块数据
        print("🏢 测试板块数据...")
        sectors = await tushare_service.get_sectors()
        print(f"   ✅ 获取到 {len(sectors['sectors'])} 个板块")
        
        # 测试排行榜
        print("🏆 测试排行榜...")
        rankings = await tushare_service.get_rankings(limit=10)
        print(f"   ✅ 获取到 {len(rankings['rankings'])} 个排行")
        
        print("\n🎉 所有API测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_all_apis())
