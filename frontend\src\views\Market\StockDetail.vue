<template>
  <div class="stock-detail-page">
    <!-- 页面头部 -->
    <div class="stock-header">
      <div class="container mx-auto px-4 py-6">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <el-button
              type="text"
              @click="goBack"
              class="text-gray-600 hover:text-gray-800"
            >
              <el-icon><ArrowLeft /></el-icon>
              返回
            </el-button>
            <div>
              <h1 class="text-2xl font-bold text-gray-800">
                {{ stockInfo.name || '加载中...' }}
                <span class="text-lg text-gray-500 ml-2">{{ symbol }}</span>
              </h1>
              <p class="text-sm text-gray-500">{{ stockInfo.industry || '' }}</p>
            </div>
          </div>
          <div class="flex items-center space-x-3">
            <el-button
              :type="isInWatchlist ? 'danger' : 'primary'"
              @click="toggleWatchlist"
              :loading="watchlistLoading"
            >
              <el-icon><Star /></el-icon>
              {{ isInWatchlist ? '取消自选' : '加入自选' }}
            </el-button>
            <el-button type="success" @click="showTradeDialog = true">
              <el-icon><TrendCharts /></el-icon>
              交易
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="container mx-auto px-4 pb-6">
      <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
        <!-- 左侧：价格信息和关键指标 -->
        <div class="xl:col-span-1">
          <!-- 实时价格卡片 -->
          <el-card class="price-card mb-6" v-loading="loading.quote">
            <div class="text-center">
              <div class="price-display">
                <span class="current-price" :class="priceChangeClass">
                  ¥{{ formatPrice(quote.price) }}
                </span>
                <div class="price-change mt-2">
                  <span :class="priceChangeClass" class="change-amount">
                    {{ quote.change >= 0 ? '+' : '' }}{{ formatPrice(quote.change) }}
                  </span>
                  <span :class="priceChangeClass" class="change-percent ml-2">
                    ({{ quote.change_pct >= 0 ? '+' : '' }}{{ quote.change_pct?.toFixed(2) }}%)
                  </span>
                </div>
              </div>
              <div class="update-time mt-3 text-xs text-gray-500">
                更新时间: {{ quote.timestamp || '暂无数据' }}
              </div>
            </div>
          </el-card>

          <!-- 关键指标 -->
          <el-card class="metrics-card" v-loading="loading.quote">
            <template #header>
              <span class="font-semibold">关键指标</span>
            </template>
            <div class="metrics-grid">
              <div class="metric-item">
                <span class="metric-label">开盘价</span>
                <span class="metric-value">¥{{ formatPrice(quote.open) }}</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">最高价</span>
                <span class="metric-value text-red-600">¥{{ formatPrice(quote.high) }}</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">最低价</span>
                <span class="metric-value text-green-600">¥{{ formatPrice(quote.low) }}</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">昨收价</span>
                <span class="metric-value">¥{{ formatPrice(quote.close) }}</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">成交量</span>
                <span class="metric-value">{{ formatVolume(quote.volume) }}</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">成交额</span>
                <span class="metric-value">{{ formatAmount(quote.turnover) }}</span>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 右侧：K线图表 -->
        <div class="xl:col-span-3">
          <el-card class="chart-card" v-loading="loading.kline">
            <template #header>
              <div class="flex items-center justify-between">
                <span class="font-semibold">K线图表</span>
                <div class="chart-controls">
                  <el-radio-group v-model="selectedPeriod" size="small" @change="handlePeriodChange">
                    <el-radio-button label="1m">分时</el-radio-button>
                    <el-radio-button label="5m">5分</el-radio-button>
                    <el-radio-button label="15m">15分</el-radio-button>
                    <el-radio-button label="30m">30分</el-radio-button>
                    <el-radio-button label="1h">1小时</el-radio-button>
                    <el-radio-button label="1d">日K</el-radio-button>
                    <el-radio-button label="1w">周K</el-radio-button>
                    <el-radio-button label="1M">月K</el-radio-button>
                  </el-radio-group>
                </div>
              </div>
            </template>

            <!-- K线图组件 -->
            <div class="chart-container" style="height: 500px;">
              <KLineChart
                v-if="klineData.length > 0"
                :symbol="symbol"
                :symbol-name="stockInfo.name"
                :chart-data="klineData"
                :selected-period="selectedPeriod"
                height="500px"
                @period-change="handlePeriodChange"
                @data-update="handleChartDataUpdate"
              />
              <div v-else class="chart-placeholder">
                <el-empty description="暂无图表数据" />
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </div>

    <!-- 交易对话框 -->
    <el-dialog
      v-model="showTradeDialog"
      title="股票交易"
      width="500px"
      :before-close="handleTradeDialogClose"
    >
      <div class="trade-form">
        <p class="mb-4">
          <span class="font-semibold">{{ stockInfo.name }}</span>
          <span class="text-gray-500 ml-2">{{ symbol }}</span>
        </p>
        <p class="mb-4">
          当前价格: <span :class="priceChangeClass" class="font-semibold">¥{{ formatPrice(quote.price) }}</span>
        </p>
        <el-form :model="tradeForm" label-width="80px">
          <el-form-item label="交易类型">
            <el-radio-group v-model="tradeForm.type">
              <el-radio label="buy">买入</el-radio>
              <el-radio label="sell">卖出</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="价格类型">
            <el-radio-group v-model="tradeForm.priceType">
              <el-radio label="market">市价</el-radio>
              <el-radio label="limit">限价</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="数量" v-if="tradeForm.priceType === 'limit'">
            <el-input-number v-model="tradeForm.price" :min="0" :step="0.01" />
          </el-form-item>
          <el-form-item label="数量">
            <el-input-number v-model="tradeForm.quantity" :min="100" :step="100" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showTradeDialog = false">取消</el-button>
          <el-button
            :type="tradeForm.type === 'buy' ? 'success' : 'danger'"
            @click="handleTrade"
            :loading="tradeLoading"
          >
            {{ tradeForm.type === 'buy' ? '买入' : '卖出' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Star, TrendCharts } from '@element-plus/icons-vue'
import KLineChart from '@/components/charts/KLineChart/index.vue'
import { useMarketStore } from '@/stores/modules/market'
import type { KLineData, QuoteData } from '@/types/market'

// 路由和状态
const route = useRoute()
const router = useRouter()
const marketStore = useMarketStore()

// 响应式数据
const symbol = ref(route.params.symbol as string)
const loading = ref({
  quote: false,
  kline: false,
  stockInfo: false
})

// 股票基本信息
const stockInfo = ref({
  name: '',
  industry: '',
  market: '',
  listDate: ''
})

// 实时行情数据
const quote = ref<QuoteData>({
  symbol: symbol.value,
  name: '',
  currentPrice: 0,
  change: 0,
  changePercent: 0,
  high: 0,
  low: 0,
  openPrice: 0,
  volume: 0,
  turnover: 0,
  timestamp: ''
})

// K线数据
const klineData = ref<KLineData[]>([])
const selectedPeriod = ref('1d')

// 自选股相关
const isInWatchlist = ref(false)
const watchlistLoading = ref(false)

// 交易相关
const showTradeDialog = ref(false)
const tradeLoading = ref(false)
const tradeForm = ref({
  type: 'buy',
  priceType: 'market',
  price: 0,
  quantity: 100
})

// 定时器
let quoteTimer: NodeJS.Timeout | null = null

// 计算属性
const priceChangeClass = computed(() => {
  if (quote.value.change > 0) return 'text-red-600'
  if (quote.value.change < 0) return 'text-green-600'
  return 'text-gray-600'
})

// 格式化函数
const formatPrice = (price: number | undefined): string => {
  if (!price || price === 0) return '0.00'
  return price.toFixed(2)
}

const formatVolume = (volume: number | undefined): string => {
  if (!volume || volume === 0) return '0'
  if (volume >= 100000000) {
    return (volume / 100000000).toFixed(2) + '亿'
  } else if (volume >= 10000) {
    return (volume / 10000).toFixed(2) + '万'
  }
  return volume.toString()
}

const formatAmount = (amount: number | undefined): string => {
  if (!amount || amount === 0) return '0'
  if (amount >= 100000000) {
    return (amount / 100000000).toFixed(2) + '亿'
  } else if (amount >= 10000) {
    return (amount / 10000).toFixed(2) + '万'
  }
  return amount.toFixed(2)
}

// 方法
const goBack = () => {
  router.back()
}

const fetchStockInfo = async () => {
  loading.value.stockInfo = true
  try {
    // 这里应该调用API获取股票基本信息
    // 暂时使用模拟数据
    stockInfo.value = {
      name: '平安银行',
      industry: '银行',
      market: 'SZ',
      listDate: '1991-04-03'
    }
  } catch (error) {
    console.error('获取股票信息失败:', error)
    ElMessage.error('获取股票信息失败')
  } finally {
    loading.value.stockInfo = false
  }
}

const fetchQuote = async () => {
  loading.value.quote = true
  try {
    const quoteData = await marketStore.fetchQuote(symbol.value)
    quote.value = {
      symbol: symbol.value,
      name: quoteData.name || stockInfo.value.name,
      currentPrice: quoteData.price || quoteData.currentPrice || 0,
      change: quoteData.change || 0,
      changePercent: quoteData.change_pct || quoteData.changePercent || 0,
      high: quoteData.high || quoteData.highPrice || 0,
      low: quoteData.low || quoteData.lowPrice || 0,
      openPrice: quoteData.open || quoteData.openPrice || 0,
      volume: quoteData.volume || 0,
      turnover: quoteData.turnover || quoteData.amount || 0,
      timestamp: quoteData.timestamp || new Date().toLocaleString()
    }
  } catch (error) {
    console.error('获取实时行情失败:', error)
    ElMessage.error('获取实时行情失败')
  } finally {
    loading.value.quote = false
  }
}

const fetchKlineData = async () => {
  loading.value.kline = true
  try {
    const data = await marketStore.fetchKlineData(symbol.value, selectedPeriod.value)
    klineData.value = data.map(item => ({
      timestamp: item.timestamp,
      open: item.open,
      high: item.high,
      low: item.low,
      close: item.close,
      volume: item.volume,
      turnover: item.turnover || 0
    }))
  } catch (error) {
    console.error('获取K线数据失败:', error)
    ElMessage.error('获取K线数据失败')
  } finally {
    loading.value.kline = false
  }
}

const handlePeriodChange = (period: string) => {
  selectedPeriod.value = period
  fetchKlineData()
}

const handleChartDataUpdate = (data: KLineData[]) => {
  klineData.value = data
}

const toggleWatchlist = async () => {
  watchlistLoading.value = true
  try {
    if (isInWatchlist.value) {
      // 移除自选
      await marketStore.removeFromWatchlist(symbol.value)
      isInWatchlist.value = false
      ElMessage.success('已移除自选')
    } else {
      // 添加自选
      await marketStore.addToWatchlist({
        symbol: symbol.value,
        name: stockInfo.value.name
      })
      isInWatchlist.value = true
      ElMessage.success('已添加自选')
    }
  } catch (error) {
    console.error('操作自选股失败:', error)
    ElMessage.error('操作失败')
  } finally {
    watchlistLoading.value = false
  }
}

const handleTradeDialogClose = () => {
  showTradeDialog.value = false
  // 重置表单
  tradeForm.value = {
    type: 'buy',
    priceType: 'market',
    price: 0,
    quantity: 100
  }
}

const handleTrade = async () => {
  try {
    await ElMessageBox.confirm(
      `确认${tradeForm.value.type === 'buy' ? '买入' : '卖出'} ${tradeForm.value.quantity} 股 ${stockInfo.value.name}？`,
      '确认交易',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    tradeLoading.value = true

    // 这里应该调用交易API
    // 模拟交易延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('交易提交成功')
    showTradeDialog.value = false

  } catch (error) {
    if (error !== 'cancel') {
      console.error('交易失败:', error)
      ElMessage.error('交易失败')
    }
  } finally {
    tradeLoading.value = false
  }
}

// 启动实时数据更新
const startRealTimeUpdate = () => {
  // 立即获取一次数据
  fetchQuote()

  // 每30秒更新一次实时行情
  quoteTimer = setInterval(() => {
    fetchQuote()
  }, 30000)
}

const stopRealTimeUpdate = () => {
  if (quoteTimer) {
    clearInterval(quoteTimer)
    quoteTimer = null
  }
}

// 监听路由参数变化
watch(() => route.params.symbol, (newSymbol) => {
  if (newSymbol && newSymbol !== symbol.value) {
    symbol.value = newSymbol as string
    // 重新加载数据
    fetchStockInfo()
    fetchKlineData()
    startRealTimeUpdate()
  }
})

// 生命周期
onMounted(() => {
  console.log('股票详情页面加载，股票代码:', symbol.value)

  // 初始化数据
  fetchStockInfo()
  fetchKlineData()
  startRealTimeUpdate()

  // 检查是否在自选股中
  isInWatchlist.value = marketStore.isInWatchlist(symbol.value)
})

onUnmounted(() => {
  stopRealTimeUpdate()
})
</script>

<style scoped>
.stock-detail-page {
  min-height: calc(100vh - 64px);
  background-color: #f8fafc;
}

.stock-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.stock-header .el-button {
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

.stock-header .el-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

.price-card {
  border: none;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
}

.price-display {
  padding: 20px 0;
}

.current-price {
  font-size: 2.5rem;
  font-weight: bold;
  display: block;
}

.price-change {
  font-size: 1.1rem;
  font-weight: 600;
}

.change-amount {
  font-size: 1.2rem;
}

.change-percent {
  font-size: 1rem;
}

.metrics-card {
  border: none;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f1f5f9;
}

.metric-item:last-child {
  border-bottom: none;
}

.metric-label {
  color: #64748b;
  font-size: 0.875rem;
}

.metric-value {
  font-weight: 600;
  color: #1e293b;
}

.chart-card {
  border: none;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
}

.chart-container {
  position: relative;
}

.chart-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #f8fafc;
  border-radius: 8px;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.trade-form {
  padding: 20px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1280px) {
  .stock-detail-page {
    padding: 0 16px;
  }

  .current-price {
    font-size: 2rem;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .stock-header .flex {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .current-price {
    font-size: 1.8rem;
  }

  .chart-controls {
    flex-wrap: wrap;
  }

  .chart-controls .el-radio-group {
    flex-wrap: wrap;
  }

  .chart-container {
    height: 300px !important;
  }
}

/* 价格颜色主题 */
.text-red-600 {
  color: #dc2626 !important;
}

.text-green-600 {
  color: #16a34a !important;
}

.text-gray-600 {
  color: #4b5563 !important;
}

/* 动画效果 */
.price-display {
  transition: all 0.3s ease;
}

.metric-value {
  transition: color 0.3s ease;
}

.el-card {
  transition: box-shadow 0.3s ease;
}

.el-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}
</style>
