<template>
  <div class="slide-verify-page">
    <div class="verify-container">
      <div class="verify-header">
        <h2>安全验证</h2>
        <p>为了确保账户安全，请完成以下滑块验证</p>
        <div v-if="username" class="user-info">
          <el-tag type="info" size="small">
            当前用户: {{ username }}
          </el-tag>
        </div>
      </div>

      <div class="verify-content">
        <!-- 简化的滑块验证 -->
        <div class="simple-verify">
          <div class="verify-track">
            <div class="verify-slider" :style="{ left: sliderPosition + 'px' }" @mousedown="startDrag"></div>
          </div>
          <p class="verify-text">{{ verifyText }}</p>
        </div>

        <div class="verify-status" v-if="statusMessage">
          <el-alert
            :title="statusMessage"
            :type="statusType"
            :closable="false"
            show-icon
          />
        </div>

        <div class="verify-actions">
          <el-button @click="goBack" size="large">
            返回登录
          </el-button>
          <el-button
            type="primary"
            @click="simulateSuccess"
            size="large"
          >
            模拟验证成功
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'

// Router and Store
const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// State
const username = ref('')
const sliderPosition = ref(0)
const verifyText = ref('向右滑动滑块完成验证')
const statusMessage = ref('')
const statusType = ref<'success' | 'warning' | 'error' | 'info'>('info')

// Methods
const startDrag = (e: MouseEvent) => {
  const startX = e.clientX
  const startPos = sliderPosition.value

  const onMouseMove = (e: MouseEvent) => {
    const deltaX = e.clientX - startX
    const newPos = Math.max(0, Math.min(300, startPos + deltaX))
    sliderPosition.value = newPos

    if (newPos > 250) {
      onVerifySuccess()
      document.removeEventListener('mousemove', onMouseMove)
      document.removeEventListener('mouseup', onMouseUp)
    }
  }

  const onMouseUp = () => {
    document.removeEventListener('mousemove', onMouseMove)
    document.removeEventListener('mouseup', onMouseUp)
    
    if (sliderPosition.value < 250) {
      sliderPosition.value = 0
      statusMessage.value = '验证失败，请重试'
      statusType.value = 'error'
    }
  }

  document.addEventListener('mousemove', onMouseMove)
  document.addEventListener('mouseup', onMouseUp)
}

const onVerifySuccess = async () => {
  statusMessage.value = '验证成功！正在跳转...'
  statusType.value = 'success'
  verifyText.value = '验证成功！'

  ElMessage.success('滑块验证成功！')

  try {
    // 获取跳转目标
    const redirect = route.query.redirect as string
    let targetUrl = redirect || '/dashboard'

    // 确保URL格式正确
    if (targetUrl && !targetUrl.startsWith('/')) {
      targetUrl = '/' + targetUrl
    }

    console.log('✅ 滑块验证成功，准备跳转到:', targetUrl)

    // 延迟跳转以显示成功消息
    setTimeout(async () => {
      try {
        await router.replace(targetUrl)
        console.log('✅ 跳转成功:', targetUrl)
      } catch (error) {
        console.error('❌ 跳转失败:', error)
        // 如果跳转失败，尝试跳转到默认页面
        try {
          await router.replace('/dashboard')
          console.log('✅ 备用跳转成功: /dashboard')
        } catch (fallbackError) {
          console.error('❌ 备用跳转也失败:', fallbackError)
          ElMessage.error('跳转失败，请手动刷新页面')
        }
      }
    }, 1500)

  } catch (error) {
    console.error('❌ 验证成功处理失败:', error)
    ElMessage.error('跳转失败，请手动刷新页面')
  }
}

const simulateSuccess = () => {
  sliderPosition.value = 300
  onVerifySuccess()
}

const goBack = () => {
  router.push('/login')
}

// Mounted
onMounted(() => {
  // 检查是否已登录
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }
  
  // 获取用户名
  const userInfo = userStore.userInfo
  if (userInfo && userInfo.username) {
    username.value = userInfo.username
  }
})
</script>

<style scoped>
.slide-verify-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.verify-container {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  width: 100%;
  text-align: center;
}

.verify-header h2 {
  color: #333;
  margin-bottom: 10px;
  font-size: 24px;
}

.verify-header p {
  color: #666;
  margin-bottom: 20px;
}

.user-info {
  margin-bottom: 30px;
}

.simple-verify {
  margin: 30px 0;
}

.verify-track {
  position: relative;
  width: 350px;
  height: 40px;
  background: #f0f0f0;
  border-radius: 20px;
  margin: 0 auto 20px;
  border: 2px solid #ddd;
}

.verify-slider {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 36px;
  height: 36px;
  background: #409eff;
  border-radius: 18px;
  cursor: pointer;
  transition: left 0.1s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.verify-slider:before {
  content: '→';
  color: white;
  font-weight: bold;
}

.verify-text {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.verify-status {
  margin: 20px 0;
}

.verify-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 30px;
}

.verify-actions .el-button {
  min-width: 120px;
}
</style>
