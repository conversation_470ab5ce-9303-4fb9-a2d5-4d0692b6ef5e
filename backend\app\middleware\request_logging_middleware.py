"""
请求/响应日志中间件
提供详细的HTTP请求和响应日志记录，支持敏感数据过滤和性能监控
"""

import json
import time
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Set
from urllib.parse import urlparse

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp, Message, Receive, Scope, Send

from app.core.logging_config import (
    get_contextual_logger,
    set_request_context,  
    clear_request_context,
    log_performance_metric,
    log_audit_event,
    log_security_event
)


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """增强的请求日志中间件"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        # 使用固定配置，避免参数传递问题
        self.log_request_body = True
        self.log_response_body = False
        self.max_body_size = 10240  # 10KB
        self.enable_performance_logging = True
        self.enable_audit_logging = True
        
        # 敏感信息过滤配置
        self.sensitive_headers = {
            'authorization', 'cookie', 'x-api-key', 'x-auth-token',
            'authorization', 'proxy-authorization', 'x-csrf-token'
        }

        self.sensitive_paths = {
            '/api/v1/auth/login', '/api/v1/auth/register',
            '/api/v1/user/change-password', '/api/v1/auth/reset-password'
        }
        
        # 需要记录完整请求体的路径（用于审计）
        self.audit_paths = {
            '/api/v1/trading/order', '/api/v1/trading/cancel',
            '/api/v1/user/profile', '/api/v1/auth/login',
            '/api/v1/strategy/create', '/api/v1/strategy/update'
        }
        
        # 性能监控阈值
        self.slow_request_threshold = 1.0  # 1秒
        self.very_slow_request_threshold = 5.0  # 5秒
        
        self.logger = get_contextual_logger("request_logging")
    
    async def dispatch(self, request: Request, call_next):
        """处理请求并记录详细日志"""
        # 生成请求ID（如果还没有的话）
        request_id = getattr(request.state, 'request_id', str(uuid.uuid4()))
        request.state.request_id = request_id
        
        # 设置日志上下文
        user_id = getattr(request.state, 'user_id', '')
        session_id = getattr(request.state, 'session_id', request.headers.get('session-id', ''))
        set_request_context(request_id, user_id, session_id)
        
        start_time = time.time()
        
        try:
            # 记录请求开始
            await self._log_request_start(request, request_id)
            
            # 获取并记录请求体（如果需要）
            request_body = None
            if self.log_request_body and self._should_log_request_body(request):
                request_body = await self._get_request_body(request)
            
            # 执行请求
            response = await call_next(request)
            
            # 计算请求时长
            duration = time.time() - start_time
            
            # 获取响应体（如果需要）
            response_body = None
            if self.log_response_body and self._should_log_response_body(request, response):
                response_body = await self._get_response_body(response)
            
            # 记录请求完成
            await self._log_request_complete(
                request, response, request_id, duration, 
                request_body, response_body
            )
            
            # 性能监控
            if self.enable_performance_logging:
                await self._log_performance_metrics(request, response, duration, request_id)
            
            # 审计日志
            if self.enable_audit_logging and self._should_audit_request(request):
                await self._log_audit_trail(request, response, request_id, request_body)
            
            return response
            
        except Exception as exc:
            duration = time.time() - start_time
            await self._log_request_error(request, exc, request_id, duration)
            raise
        
        finally:
            clear_request_context()
    
    async def _log_request_start(self, request: Request, request_id: str):
        """记录请求开始"""
        headers = self._filter_sensitive_headers(dict(request.headers))
        
        log_data = {
            "event": "request_start",
            "request_id": request_id,
            "method": request.method,
            "url": str(request.url),
            "path": request.url.path,
            "query_params": dict(request.query_params),
            "headers": headers,
            "client_ip": self._get_client_ip(request),
            "user_agent": request.headers.get("user-agent"),
            "content_type": request.headers.get("content-type"),
            "content_length": request.headers.get("content-length"),
            "timestamp": datetime.utcnow().isoformat()
        }
        
        self.logger.info("HTTP Request Started", **log_data)
    
    async def _log_request_complete(
        self, 
        request: Request, 
        response: Response, 
        request_id: str, 
        duration: float,
        request_body: Optional[str] = None,
        response_body: Optional[str] = None
    ):
        """记录请求完成"""
        log_data = {
            "event": "request_complete",
            "request_id": request_id,
            "method": request.method,
            "url": str(request.url),
            "path": request.url.path,
            "status_code": response.status_code,
            "duration": round(duration, 3),
            "response_headers": dict(response.headers),
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # 添加请求体（如果有且不敏感）
        if request_body and not self._is_sensitive_path(request.url.path):
            log_data["request_body"] = self._truncate_body(request_body)
        
        # 添加响应体（如果有）
        if response_body:
            log_data["response_body"] = self._truncate_body(response_body)
        
        # 根据状态码和响应时间选择日志级别
        if response.status_code >= 500:
            self.logger.error("HTTP Request Failed (Server Error)", **log_data)
        elif response.status_code >= 400:
            self.logger.warning("HTTP Request Failed (Client Error)", **log_data)
        elif duration > self.very_slow_request_threshold:
            self.logger.warning("HTTP Request Completed (Very Slow)", **log_data)
        elif duration > self.slow_request_threshold:
            self.logger.info("HTTP Request Completed (Slow)", **log_data)
        else:
            self.logger.info("HTTP Request Completed", **log_data)
    
    async def _log_request_error(
        self, 
        request: Request, 
        exc: Exception, 
        request_id: str, 
        duration: float
    ):
        """记录请求错误"""
        log_data = {
            "event": "request_error",
            "request_id": request_id,
            "method": request.method,
            "url": str(request.url),
            "path": request.url.path,
            "duration": round(duration, 3),
            "exception_type": type(exc).__name__,
            "exception_message": str(exc),
            "timestamp": datetime.utcnow().isoformat()
        }
        
        self.logger.error("HTTP Request Error", **log_data)
    
    async def _log_performance_metrics(
        self, 
        request: Request, 
        response: Response, 
        duration: float, 
        request_id: str
    ):
        """记录性能指标"""
        # 记录慢请求
        if duration > self.slow_request_threshold:
            log_performance_metric(
                operation=f"{request.method} {request.url.path}",
                duration=duration,
                metadata={
                    "status_code": response.status_code,
                    "request_id": request_id,
                    "query_params_count": len(request.query_params),
                    "headers_count": len(request.headers)
                }
            )
    
    async def _log_audit_trail(
        self, 
        request: Request, 
        response: Response, 
        request_id: str,
        request_body: Optional[str] = None
    ):
        """记录审计跟踪"""
        # 构建审计数据
        audit_details = {
            "method": request.method,
            "path": request.url.path,
            "query_params": dict(request.query_params),
            "status_code": response.status_code,
            "client_ip": self._get_client_ip(request),
            "user_agent": request.headers.get("user-agent"),
            "request_id": request_id
        }
        
        # 添加请求体（仅用于审计路径）
        if request_body and request.url.path in self.audit_paths:
            audit_details["request_data"] = self._sanitize_audit_data(request_body)
        
        log_audit_event(
            action=f"{request.method} {request.url.path}",
            resource=request.url.path,
            result="success" if response.status_code < 400 else "failure",
            details=audit_details
        )
    
    async def _get_request_body(self, request: Request) -> Optional[str]:
        """获取请求体内容"""
        try:
            body = await request.body()
            if len(body) > self.max_body_size:
                return f"[Body too large: {len(body)} bytes, truncated]"
            
            # 尝试解码为文本
            try:
                return body.decode('utf-8')
            except UnicodeDecodeError:
                return f"[Binary content: {len(body)} bytes]"
                
        except Exception as e:
            return f"[Error reading body: {str(e)}]"
    
    async def _get_response_body(self, response: Response) -> Optional[str]:
        """获取响应体内容"""
        # 这个功能需要特殊处理，因为响应体可能已经被消费
        # 在实际实现中，需要使用流包装器来捕获响应内容
        return None  # 简化实现
    
    def _should_log_request_body(self, request: Request) -> bool:
        """判断是否应该记录请求体"""
        # 敏感路径不记录详细请求体
        if self._is_sensitive_path(request.url.path):
            return False
        
        # 只记录POST、PUT、PATCH请求的请求体
        return request.method in ["POST", "PUT", "PATCH"]
    
    def _should_log_response_body(self, request: Request, response: Response) -> bool:
        """判断是否应该记录响应体"""
        # 只在错误响应时记录响应体
        return response.status_code >= 400
    
    def _should_audit_request(self, request: Request) -> bool:
        """判断是否需要审计此请求"""
        return (
            request.url.path in self.audit_paths or
            request.method in ["POST", "PUT", "DELETE", "PATCH"] or
            self._is_sensitive_path(request.url.path)
        )
    
    def _is_sensitive_path(self, path: str) -> bool:
        """检查是否为敏感路径"""
        return path in self.sensitive_paths
    
    def _filter_sensitive_headers(self, headers: Dict[str, str]) -> Dict[str, str]:
        """过滤敏感请求头"""
        filtered = {}
        for key, value in headers.items():
            if key.lower() in self.sensitive_headers:
                filtered[key] = "[FILTERED]"
            else:
                filtered[key] = value
        return filtered
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 检查代理头
        forwarded = request.headers.get("x-forwarded-for")
        if forwarded:
            return forwarded.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # 回退到连接信息
        return request.client.host if request.client else "unknown"
    
    def _truncate_body(self, body: str) -> str:
        """截断请求/响应体"""
        if len(body) > self.max_body_size:
            return body[:self.max_body_size] + f"... [truncated, total: {len(body)} chars]"
        return body
    
    def _sanitize_audit_data(self, data: str) -> str:
        """清理审计数据，移除敏感信息"""
        try:
            # 尝试解析JSON并移除敏感字段
            parsed = json.loads(data)
            if isinstance(parsed, dict):
                sensitive_fields = {'password', 'token', 'secret', 'key', 'credential'}
                for field in sensitive_fields:
                    if field in parsed:
                        parsed[field] = "[FILTERED]"
                return json.dumps(parsed)
        except json.JSONDecodeError:
            pass
        
        # 对于非JSON数据，简单替换常见的敏感模式
        import re
        data = re.sub(r'"password"\s*:\s*"[^"]*"', '"password":"[FILTERED]"', data)
        data = re.sub(r'"token"\s*:\s*"[^"]*"', '"token":"[FILTERED]"', data)
        data = re.sub(r'"secret"\s*:\s*"[^"]*"', '"secret":"[FILTERED]"', data)
        
        return data


class ResponseBodyCapturingMiddleware(BaseHTTPMiddleware):
    """响应体捕获中间件 - 如果需要记录响应体，可以使用此中间件"""
    
    def __init__(self, app: ASGIApp, max_size: int = 10240):
        super().__init__(app)
        self.max_size = max_size
    
    async def dispatch(self, request: Request, call_next):
        """捕获响应体内容"""
        response = await call_next(request)
        
        # 在这里可以捕获响应体，但需要重新创建响应对象
        # 由于复杂性，这里仅作为示例
        
        return response


# 工具函数
def create_request_logging_middleware(
    log_request_body: bool = True,
    log_response_body: bool = False,
    max_body_size: int = 10240,
    enable_performance_logging: bool = True,
    enable_audit_logging: bool = True,
    additional_sensitive_headers: Optional[Set[str]] = None,
    additional_sensitive_paths: Optional[Set[str]] = None
) -> RequestLoggingMiddleware:
    """创建配置好的请求日志中间件"""
    
    sensitive_headers = {
        'authorization', 'cookie', 'x-api-key', 'x-auth-token',
        'proxy-authorization', 'x-csrf-token', 'x-session-token'
    }
    
    sensitive_paths = {
        '/api/v1/auth/login', '/api/v1/auth/register', 
        '/api/v1/user/change-password', '/api/v1/auth/reset-password'
    }
    
    if additional_sensitive_headers:
        sensitive_headers.update(additional_sensitive_headers)
    
    if additional_sensitive_paths:
        sensitive_paths.update(additional_sensitive_paths)
    
    return RequestLoggingMiddleware(
        app=None,  # 将在添加到app时设置
        log_request_body=log_request_body,
        log_response_body=log_response_body,
        max_body_size=max_body_size,
        sensitive_headers=sensitive_headers,
        sensitive_paths=sensitive_paths,
        enable_performance_logging=enable_performance_logging,
        enable_audit_logging=enable_audit_logging
    )