"""
数据源管理器 - 统一数据源选择与切换
实现三层数据源优先级：Tushare -> AkShare -> Mock
"""

import logging
from enum import Enum
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass
from abc import ABC, abstractmethod

from app.core.config import get_settings
from app.core.exceptions import DataSourceException, MarketDataException

logger = logging.getLogger(__name__)
settings = get_settings()


class DataSourceType(str, Enum):
    """数据源类型枚举"""
    TUSHARE = "tushare"
    AKSHARE = "akshare" 
    MOCK = "mock"


class DataSourceStatus(str, Enum):
    """数据源状态枚举"""
    AVAILABLE = "available"
    UNAVAILABLE = "unavailable"
    ERROR = "error"
    DISABLED = "disabled"


@dataclass
class DataSourceInfo:
    """数据源信息"""
    type: DataSourceType
    status: DataSourceStatus
    priority: int
    description: str
    last_check: Optional[str] = None
    error_message: Optional[str] = None


class DataSourceInterface(ABC):
    """数据源接口抽象类"""
    
    @abstractmethod
    async def get_stock_list(self, market: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取股票列表"""
        pass
    
    @abstractmethod
    async def get_realtime_quote(self, symbol: str) -> Dict[str, Any]:
        """获取实时行情"""
        pass
    
    @abstractmethod
    async def get_batch_quotes(self, symbols: List[str]) -> List[Dict[str, Any]]:
        """批量获取行情"""
        pass
    
    @abstractmethod
    async def get_kline_data(self, symbol: str, period: str = "1d", limit: int = 100) -> List[Dict[str, Any]]:
        """获取K线数据"""
        pass
    
    @abstractmethod
    async def search_stocks(self, keyword: str) -> List[Dict[str, Any]]:
        """搜索股票"""
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """健康检查"""
        pass


class TushareDataSource(DataSourceInterface):
    """Tushare数据源实现"""
    
    def __init__(self, token: Optional[str] = None):
        self.token = token or settings.TUSHARE_API_TOKEN
        self.available = bool(self.token)
        
    async def get_stock_list(self, market: Optional[str] = None) -> List[Dict[str, Any]]:
        if not self.available:
            raise DataSourceException("Tushare token not available")
        
        # TODO: 实现Tushare API调用
        logger.info(f"Fetching stock list from Tushare, market: {market}")
        # 模拟返回数据
        return [
            {
                "symbol": "000001.SZ",
                "name": "平安银行",
                "market": "SZ",
                "industry": "银行",
                "list_date": "1991-04-03"
            }
        ]
    
    async def get_realtime_quote(self, symbol: str) -> Dict[str, Any]:
        if not self.available:
            raise DataSourceException("Tushare token not available")
        
        logger.info(f"Fetching realtime quote from Tushare: {symbol}")
        # 模拟返回数据
        return {
            "symbol": symbol,
            "price": 12.34,
            "change": 0.56,
            "change_pct": 4.76,
            "volume": 123456789,
            "turnover": **********.12,
            "high": 12.50,
            "low": 11.80,
            "open": 11.90,
            "close": 11.78,
            "timestamp": "2025-01-14 15:00:00"
        }
    
    async def get_batch_quotes(self, symbols: List[str]) -> List[Dict[str, Any]]:
        if not self.available:
            raise DataSourceException("Tushare token not available")
        
        quotes = []
        for symbol in symbols:
            quote = await self.get_realtime_quote(symbol)
            quotes.append(quote)
        return quotes
    
    async def get_kline_data(self, symbol: str, period: str = "1d", limit: int = 100) -> List[Dict[str, Any]]:
        if not self.available:
            raise DataSourceException("Tushare token not available")
        
        logger.info(f"Fetching kline data from Tushare: {symbol}, period: {period}")
        # 模拟返回数据
        return [
            {
                "date": "2025-01-14",
                "open": 11.90,
                "high": 12.50,
                "low": 11.80,
                "close": 12.34,
                "volume": 123456789,
                "amount": **********.12
            }
        ]
    
    async def search_stocks(self, keyword: str) -> List[Dict[str, Any]]:
        if not self.available:
            raise DataSourceException("Tushare token not available")
        
        logger.info(f"Searching stocks from Tushare: {keyword}")
        # 模拟搜索结果
        return [
            {
                "symbol": "000001.SZ",
                "name": "平安银行",
                "market": "SZ",
                "industry": "银行"
            }
        ]
    
    async def health_check(self) -> bool:
        return self.available


class AkShareDataSource(DataSourceInterface):
    """AkShare数据源实现"""
    
    def __init__(self):
        self.available = settings.AKSHARE_ENABLED
        
    async def get_stock_list(self, market: Optional[str] = None) -> List[Dict[str, Any]]:
        if not self.available:
            raise DataSourceException("AkShare is disabled")
        
        logger.info(f"Fetching stock list from AkShare, market: {market}")
        # TODO: 实现AkShare API调用
        return [
            {
                "symbol": "000001",
                "name": "平安银行",
                "market": "SZ",
                "industry": "银行",
                "list_date": "1991-04-03"
            }
        ]
    
    async def get_realtime_quote(self, symbol: str) -> Dict[str, Any]:
        if not self.available:
            raise DataSourceException("AkShare is disabled")
        
        logger.info(f"Fetching realtime quote from AkShare: {symbol}")
        return {
            "symbol": symbol,
            "price": 12.30,
            "change": 0.52,
            "change_pct": 4.42,
            "volume": 123456789,
            "turnover": **********.12,
            "high": 12.45,
            "low": 11.85,
            "open": 11.95,
            "close": 11.78,
            "timestamp": "2025-01-14 15:00:00"
        }
    
    async def get_batch_quotes(self, symbols: List[str]) -> List[Dict[str, Any]]:
        if not self.available:
            raise DataSourceException("AkShare is disabled")
        
        quotes = []
        for symbol in symbols:
            quote = await self.get_realtime_quote(symbol)
            quotes.append(quote)
        return quotes
    
    async def get_kline_data(self, symbol: str, period: str = "1d", limit: int = 100) -> List[Dict[str, Any]]:
        if not self.available:
            raise DataSourceException("AkShare is disabled")
        
        logger.info(f"Fetching kline data from AkShare: {symbol}, period: {period}")
        return [
            {
                "date": "2025-01-14",
                "open": 11.95,
                "high": 12.45,
                "low": 11.85,
                "close": 12.30,
                "volume": 123456789,
                "amount": **********.12
            }
        ]
    
    async def search_stocks(self, keyword: str) -> List[Dict[str, Any]]:
        if not self.available:
            raise DataSourceException("AkShare is disabled")
        
        logger.info(f"Searching stocks from AkShare: {keyword}")
        return [
            {
                "symbol": "000001",
                "name": "平安银行", 
                "market": "SZ",
                "industry": "银行"
            }
        ]
    
    async def health_check(self) -> bool:
        return self.available


class MockDataSource(DataSourceInterface):
    """Mock数据源实现 - 兜底方案"""
    
    def __init__(self):
        self.available = True
        
    async def get_stock_list(self, market: Optional[str] = None) -> List[Dict[str, Any]]:
        logger.info(f"Fetching stock list from Mock, market: {market}")
        mock_stocks = [
            {"symbol": "000001", "name": "平安银行", "market": "SZ", "industry": "银行"},
            {"symbol": "000002", "name": "万科A", "market": "SZ", "industry": "房地产"},
            {"symbol": "600000", "name": "浦发银行", "market": "SH", "industry": "银行"},
            {"symbol": "600036", "name": "招商银行", "market": "SH", "industry": "银行"},
        ]
        
        if market:
            return [stock for stock in mock_stocks if stock["market"] == market]
        return mock_stocks
    
    async def get_realtime_quote(self, symbol: str) -> Dict[str, Any]:
        logger.info(f"Fetching realtime quote from Mock: {symbol}")
        import random
        base_price = 10.0 + random.random() * 20
        change = (random.random() - 0.5) * 2
        
        return {
            "symbol": symbol,
            "price": round(base_price + change, 2),
            "change": round(change, 2),
            "change_pct": round((change / base_price) * 100, 2),
            "volume": random.randint(10000000, 200000000),
            "turnover": round(random.uniform(100000000, 2000000000), 2),
            "high": round(base_price + abs(change) + random.random(), 2),
            "low": round(base_price - abs(change) - random.random(), 2),
            "open": round(base_price + (random.random() - 0.5), 2),
            "close": round(base_price, 2),
            "timestamp": "2025-01-14 15:00:00"
        }
    
    async def get_batch_quotes(self, symbols: List[str]) -> List[Dict[str, Any]]:
        quotes = []
        for symbol in symbols:
            quote = await self.get_realtime_quote(symbol)
            quotes.append(quote)
        return quotes
    
    async def get_kline_data(self, symbol: str, period: str = "1d", limit: int = 100) -> List[Dict[str, Any]]:
        logger.info(f"Fetching kline data from Mock: {symbol}, period: {period}")
        import random
        from datetime import datetime, timedelta
        
        klines = []
        base_price = 10.0 + random.random() * 20
        start_date = datetime.now() - timedelta(days=limit)
        
        for i in range(limit):
            date = start_date + timedelta(days=i)
            open_price = base_price + (random.random() - 0.5) * 2
            close_price = open_price + (random.random() - 0.5) * 2
            high_price = max(open_price, close_price) + random.random()
            low_price = min(open_price, close_price) - random.random()
            
            klines.append({
                "date": date.strftime("%Y-%m-%d"),
                "open": round(open_price, 2),
                "high": round(high_price, 2),
                "low": round(low_price, 2),
                "close": round(close_price, 2),
                "volume": random.randint(10000000, 200000000),
                "amount": round(random.uniform(100000000, 2000000000), 2)
            })
            base_price = close_price
            
        return klines
    
    async def search_stocks(self, keyword: str) -> List[Dict[str, Any]]:
        logger.info(f"Searching stocks from Mock: {keyword}")
        mock_results = [
            {"symbol": "000001", "name": "平安银行", "market": "SZ", "industry": "银行"},
            {"symbol": "600036", "name": "招商银行", "market": "SH", "industry": "银行"},
        ]
        
        # 简单的关键词匹配
        return [stock for stock in mock_results if keyword.lower() in stock["name"].lower() or keyword in stock["symbol"]]
    
    async def health_check(self) -> bool:
        return True


class DataSourceManager:
    """数据源管理器 - 实现智能切换和故障转移"""
    
    def __init__(self):
        self.sources = {}
        self.priority_order = []
        self.current_source = None
        self._initialize_sources()
    
    def _initialize_sources(self):
        """初始化数据源"""
        # 按优先级初始化数据源
        self.sources[DataSourceType.TUSHARE] = TushareDataSource()
        self.sources[DataSourceType.AKSHARE] = AkShareDataSource()
        self.sources[DataSourceType.MOCK] = MockDataSource()
        
        # 设置优先级顺序
        if settings.USE_REAL_DATA:
            if settings.PREFERRED_DATA_SOURCE == "tushare":
                self.priority_order = [DataSourceType.TUSHARE, DataSourceType.AKSHARE, DataSourceType.MOCK]
            else:
                self.priority_order = [DataSourceType.AKSHARE, DataSourceType.TUSHARE, DataSourceType.MOCK]
        else:
            self.priority_order = [DataSourceType.MOCK]
        
        logger.info(f"Data source priority order: {self.priority_order}")
    
    async def get_available_source(self) -> DataSourceInterface:
        """获取可用的数据源"""
        for source_type in self.priority_order:
            source = self.sources[source_type]
            try:
                if await source.health_check():
                    if self.current_source != source_type:
                        logger.info(f"Switching to data source: {source_type}")
                        self.current_source = source_type
                    return source
            except Exception as e:
                logger.warning(f"Data source {source_type} health check failed: {e}")
                continue
        
        raise DataSourceException("No available data source")
    
    async def get_stock_list(self, market: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取股票列表"""
        source = await self.get_available_source()
        return await source.get_stock_list(market)
    
    async def get_realtime_quote(self, symbol: str) -> Dict[str, Any]:
        """获取实时行情"""
        source = await self.get_available_source()
        return await source.get_realtime_quote(symbol)
    
    async def get_batch_quotes(self, symbols: List[str]) -> List[Dict[str, Any]]:
        """批量获取行情"""
        source = await self.get_available_source()
        return await source.get_batch_quotes(symbols)
    
    async def get_kline_data(self, symbol: str, period: str = "1d", limit: int = 100) -> List[Dict[str, Any]]:
        """获取K线数据"""
        source = await self.get_available_source()
        return await source.get_kline_data(symbol, period, limit)
    
    async def search_stocks(self, keyword: str) -> List[Dict[str, Any]]:
        """搜索股票"""
        source = await self.get_available_source()
        return await source.search_stocks(keyword)
    
    async def get_source_status(self) -> Dict[str, DataSourceInfo]:
        """获取所有数据源状态"""
        status = {}
        for source_type, source in self.sources.items():
            try:
                is_available = await source.health_check()
                status[source_type] = DataSourceInfo(
                    type=source_type,
                    status=DataSourceStatus.AVAILABLE if is_available else DataSourceStatus.UNAVAILABLE,
                    priority=self.priority_order.index(source_type) if source_type in self.priority_order else -1,
                    description=f"{source_type.title()} data source"
                )
            except Exception as e:
                status[source_type] = DataSourceInfo(
                    type=source_type,
                    status=DataSourceStatus.ERROR,
                    priority=self.priority_order.index(source_type) if source_type in self.priority_order else -1,
                    description=f"{source_type.title()} data source",
                    error_message=str(e)
                )
        
        return status


# 全局数据源管理器实例
data_source_manager = DataSourceManager()